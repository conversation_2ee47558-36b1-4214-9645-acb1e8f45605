/* 聊天列表样式 */
.chat-list {
    padding: 0;
}



/* 空聊天提示 */
.empty-chat-message {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-muted);
    font-size: 16px;
    line-height: 1.8;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: var(--border-radius-lg);
    margin: 20px;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.empty-chat-message::before {
    content: '💬';
    display: block;
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.6;
}

.chat-item {
    display: flex;
    padding: 18px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    position: relative;
    transition: all var(--transition-fast);
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    margin: 8px 12px;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
}

.chat-item:hover {
    background: rgba(255, 255, 255, 0.95);
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-md);
}

.chat-avatar {
    width: 54px;
    height: 54px;
    border-radius: var(--border-radius-md);
    background: var(--primary-gradient);
    background-size: cover;
    background-position: center;
    margin-right: 16px;
    flex-shrink: 0;
    box-shadow: var(--shadow-md);
    position: relative;
    border: 2px solid rgba(255, 255, 255, 0.8);
    transition: all var(--transition-fast);
}

.chat-item:hover .chat-avatar {
    transform: scale(1.05);
    box-shadow: var(--shadow-lg);
}

/* 群聊拼图头像 */
.chat-avatar .group-avatar-grid {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 1px;
    border-radius: inherit;
    overflow: hidden;
}

.group-avatar-cell {
    background-size: cover;
    background-position: center;
}

.group-avatar-cell.placeholder {
    background: rgba(255,255,255,0.15);
}


.chat-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
}

.chat-top {
    display: flex;
    justify-content: space-between;
    margin-bottom: 6px;
}

.chat-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
}

.chat-name-container {
    display: flex;
    align-items: center;
    gap: 6px;
}

.pin-indicator {
    color: var(--primary-color);
    font-size: 12px;
    opacity: 0.8;
}

.mute-indicator {
    color: #94a3b8;
    font-size: 12px;
    opacity: 0.8;
}

.chat-time {
    font-size: 12px;
    color: var(--dark-grey);
    margin-left: 10px;
    flex-shrink: 0;
}

.chat-message {
    font-size: 14px;
    color: var(--text-secondary);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 80%;
    line-height: 1.4;
}

.chat-badge {
    background-color: var(--red);
    color: white;
    font-size: 12px;
    min-width: 20px;
    height: 20px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 6px;
    box-shadow: 0 2px 5px rgba(255, 59, 48, 0.3);
    font-weight: 600;
    position: absolute;
    top: 6px;
    left: 40px;
    animation: badgePulse 2s infinite;
    transform-origin: center;
    z-index: 5;
}

@keyframes badgePulse {
    0% {
        transform: scale(1);
    }
    10% {
        transform: scale(1.1);
    }
    20% {
        transform: scale(1);
    }
    100% {
        transform: scale(1);
    }
}

/* 聊天详情页样式 */
.chat-detail {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    z-index: 200;
    max-width: 600px;
    margin: 0 auto;
    display: none;
    flex-direction: column;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
    overflow-x: hidden; /* 隐藏水平滚动条 */
    overflow-y: auto; /* 允许垂直滚动 */
}

.chat-detail.active {
    display: flex;
    opacity: 1;
    transform: translateY(0);
}

.chat-header {
    height: 60px;
    border-bottom: 1px solid var(--medium-grey);
    display: flex;
    align-items: center;
    padding: 0 15px;
    background-color: #fff;
    box-shadow: var(--shadow-sm);
}

.back-btn, .more-btn {
    background: none;
    border: none;
    font-size: 18px;
    color: var(--text-primary);
    cursor: pointer;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all var(--transition-fast);
}

.back-btn:hover, .more-btn:hover {
    background-color: var(--light-grey);
}

.chat-header h3 {
    flex: 1;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden; /* 隐藏水平滚动条 */
    padding: 15px;
    background-color: var(--light-grey);
    background-image:
        radial-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px),
        radial-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px);
    background-size: 20px 20px;
    background-position: 0 0, 10px 10px;
    scroll-behavior: smooth;
}

/* 加载提示和错误提示 */
.loading-messages, .error-message, .empty-messages {
    text-align: center;
    padding: 30px 20px;
    color: var(--dark-grey);
    font-size: 15px;
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: var(--border-radius-md);
    margin: 20px 0;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    box-shadow: var(--shadow-sm);
}

.error-message {
    color: var(--red);
    background-color: rgba(255, 59, 48, 0.05);
}

/* 消息样式 */
.message {
    display: flex;
    margin-bottom: 20px;
    align-items: flex-start;
    position: relative;
    transition: transform 0.3s ease;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #eee;
    background-size: cover;
    background-position: center;
    margin-right: 12px;
    box-shadow: var(--shadow-sm);
    border: 2px solid #fff;
}

.message.sent {
    flex-direction: row-reverse;
}

.message.sent .message-avatar {
    margin-right: 0;
    margin-left: 12px;
}

.message-content {
    max-width: 70%;
    position: relative;
}

.message-bubble {
    padding: 14px 18px;
    font-size: 16px;
    word-break: break-word;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
    line-height: 1.5;
    position: relative;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.message.received .message-bubble {
    background: linear-gradient(135deg,
        rgba(224, 244, 255, 0.98) 0%,
        rgba(179, 229, 255, 0.95) 50%,
        rgba(135, 206, 235, 0.92) 100%);
    border-radius: 20px 20px 20px 6px;
    color: #1e293b;
    border: 1px solid rgba(135, 206, 235, 0.3);
    box-shadow:
        0 4px 12px rgba(135, 206, 235, 0.15),
        0 2px 6px rgba(135, 206, 235, 0.1),
        0 1px 3px rgba(0, 0, 0, 0.02),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    position: relative;
    overflow: hidden;
}

.message.received .message-bubble::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: -9px;
    width: 18px;
    height: 18px;
    background: linear-gradient(135deg,
        rgba(179, 229, 255, 0.95) 0%,
        rgba(135, 206, 235, 0.92) 100%);
    border-radius: 0 0 18px 0;
    z-index: -1;
    border-right: 1px solid rgba(135, 206, 235, 0.3);
    border-bottom: 1px solid rgba(135, 206, 235, 0.3);
}

/* 添加好友消息气泡的微妙光泽效果 */
.message.received .message-bubble::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(180deg,
        rgba(255, 255, 255, 0.4) 0%,
        rgba(255, 255, 255, 0.1) 50%,
        transparent 100%);
    border-radius: 20px 20px 0 0;
    pointer-events: none;
}

/* 好友消息悬浮效果 */
.message.received:hover .message-bubble {
    background: linear-gradient(135deg,
        rgba(224, 244, 255, 1) 0%,
        rgba(179, 229, 255, 0.98) 50%,
        rgba(135, 206, 235, 0.95) 100%);
    box-shadow:
        0 6px 20px rgba(135, 206, 235, 0.25),
        0 3px 10px rgba(135, 206, 235, 0.15),
        0 1px 4px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.95);
    transform: translateY(-1px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 好友消息文字样式优化 */
.message.received .message-bubble {
    font-weight: 400;
    letter-spacing: 0.01em;
    line-height: 1.5;
}

/* 好友消息时间戳样式 */
.message.received .message-time {
    color: #64748b;
    font-size: 12px;
    opacity: 0.8;
}

.message.sent .message-bubble {
    background: var(--primary-gradient);
    color: white;
    border-radius: 20px 20px 6px 20px;
    box-shadow: var(--shadow-md), 0 0 20px rgba(7, 193, 96, 0.2);
}

.message.sent .message-bubble::before {
    content: '';
    position: absolute;
    bottom: 0;
    right: -9px;
    width: 18px;
    height: 18px;
    background: var(--primary-color);
    border-radius: 0 0 0 18px;
    z-index: -1;
}

.message-time {
    font-size: 12px;
    color: var(--dark-grey);
    margin-top: 5px;
    text-align: right;
    opacity: 0.8;
}

.message.received .message-time {
    text-align: left;
}

/* 未读消息提示条 */
.unread-indicator {
    position: absolute;
    bottom: 160px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(7, 193, 96, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    color: white;
    padding: 10px 18px;
    border-radius: 24px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 6px 20px rgba(7, 193, 96, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.3);
    z-index: 150;
    animation: slideUpFadeIn 0.3s ease-out;
    font-size: 14px;
    font-weight: 500;
    min-width: 140px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.unread-indicator:hover {
    background: rgba(7, 193, 96, 1);
    transform: translateX(-50%) translateY(-3px);
    box-shadow: 0 8px 25px rgba(7, 193, 96, 0.5);
}

.unread-indicator:active {
    transform: translateX(-50%) translateY(-1px);
    box-shadow: 0 4px 15px rgba(7, 193, 96, 0.4);
}

.unread-indicator-content {
    display: flex;
    align-items: center;
    gap: 4px;
}

.unread-count {
    font-weight: 600;
    font-size: 15px;
}

.unread-text {
    font-size: 13px;
    opacity: 0.9;
}

.unread-scroll-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.unread-scroll-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.unread-scroll-btn i {
    font-size: 12px;
}

@keyframes slideUpFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 输入区域样式 - 现代化大厂风格 */
.chat-input-area {
    padding: 20px 16px 16px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.98) 100%);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-top: 1px solid rgba(255, 255, 255, 0.3);
    display: flex;
    flex-direction: column;
    box-shadow:
        0 -8px 32px rgba(0, 0, 0, 0.08),
        0 -4px 16px rgba(0, 0, 0, 0.04),
        0 -2px 8px rgba(0, 0, 0, 0.02);
    border-radius: 20px 20px 0 0;
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-bottom: none;
    position: relative;
}

/* 功能按钮栏 */
.chat-function-bar {
    display: flex;
    gap: 12px;
    margin-bottom: 16px;
    padding: 0 4px;
    justify-content: flex-start;
    align-items: center;
}

.function-btn {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    border-radius: 16px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.1),
        0 2px 6px rgba(0, 0, 0, 0.06),
        0 1px 3px rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
    border: none;
    flex-shrink: 0;
}

/* 表情按钮 - 默认橙色 */
.emoji-btn {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
}

.emoji-btn:hover {
    background: linear-gradient(135deg, #ffb74d 0%, #ff9800 100%);
    transform: translateY(-3px) scale(1.1);
    box-shadow:
        0 8px 25px rgba(255, 152, 0, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.15),
        0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 图片按钮 - 默认紫色 */
.media-btn {
    background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
    color: white;
}

.media-btn:hover {
    background: linear-gradient(135deg, #ba68c8 0%, #9c27b0 100%);
    transform: translateY(-3px) scale(1.1);
    box-shadow:
        0 8px 25px rgba(156, 39, 176, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.15),
        0 2px 6px rgba(0, 0, 0, 0.1);
}

/* 文件按钮 - 默认绿色 */
.file-btn {
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    color: white;
}

.file-btn:hover {
    background: linear-gradient(135deg, #66bb6a 0%, #4caf50 100%);
    transform: translateY(-3px) scale(1.1);
    box-shadow:
        0 8px 25px rgba(76, 175, 80, 0.4),
        0 4px 12px rgba(0, 0, 0, 0.15),
        0 2px 6px rgba(0, 0, 0, 0.1);
}

.function-btn:active {
    transform: translateY(-1px) scale(1.05);
    transition: all 0.1s ease;
}

/* 添加按钮内部光晕效果 */
.function-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.function-btn:hover::before {
    width: 40px;
    height: 40px;
    opacity: 1;
}

/* 图标统一样式和动画 */
.function-btn i {
    font-size: 20px !important;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.function-btn:hover i {
    transform: scale(1.15);
}

/* 输入框容器 */
.chat-input-container {
    display: flex;
    align-items: flex-end;
    gap: 12px;
}

.input-wrapper {
    flex: 1;
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%);
    border-radius: 24px;
    border: 2px solid transparent;
    box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
}

/* 渐变边框效果 */
.input-wrapper::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
        rgba(7, 193, 96, 0.3) 0%,
        rgba(76, 175, 80, 0.3) 50%,
        rgba(156, 39, 176, 0.3) 100%);
    border-radius: 26px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.input-wrapper:focus-within::before {
    opacity: 1;
}

.input-wrapper:focus-within {
    transform: translateY(-2px);
    box-shadow:
        0 8px 25px rgba(7, 193, 96, 0.15),
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 6px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

#chat-input {
    width: 100%;
    border: none;
    background: transparent;
    padding: 16px 20px;
    font-size: 16px;
    resize: none;
    outline: none;
    min-height: 24px;
    max-height: 120px;
    line-height: 1.5;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    color: #333;
    overflow-y: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

#chat-input::-webkit-scrollbar {
    display: none;
}

#chat-input::placeholder {
    color: #999;
    font-weight: 400;
}



/* 发送按钮 */
.send-button {
    width: 52px;
    height: 52px;
    border-radius: 50%;
    background: linear-gradient(135deg, #07c160 0%, #4caf50 100%);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 6px 20px rgba(7, 193, 96, 0.3),
        0 3px 10px rgba(0, 0, 0, 0.1),
        0 1px 4px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.send-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.send-button:hover::before {
    opacity: 1;
}

.send-button:hover {
    transform: scale(1.1) translateY(-2px);
    box-shadow:
        0 8px 30px rgba(7, 193, 96, 0.4),
        0 4px 15px rgba(0, 0, 0, 0.15),
        0 2px 8px rgba(0, 0, 0, 0.1);
}

.send-button:active {
    transform: scale(0.95) translateY(0);
    transition: all 0.1s ease;
}

.send-button:disabled {
    background: linear-gradient(135deg, #e0e0e0 0%, #bdbdbd 100%);
    color: #999;
    cursor: not-allowed;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        0 1px 4px rgba(0, 0, 0, 0.06);
}

.send-button:disabled:hover {
    transform: none;
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.1),
        0 1px 4px rgba(0, 0, 0, 0.06);
}

.send-button i {
    font-size: 18px;
    margin-left: 2px;
}

/* 图片和视频消息样式增强 */
.image-container {
    position: relative;
    display: inline-block;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.image-container:hover {
    transform: scale(1.03);
}

.message-image {
    max-width: 240px;
    max-height: 300px;
    border-radius: 12px;
    cursor: pointer;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
    border: 2px solid #fff;
    display: block;
}

.video-container {
    position: relative;
    display: inline-block;
    transition: all var(--transition-fast);
}

.video-container:hover {
    transform: scale(1.03);
}

.message-video {
    max-width: 240px;
    max-height: 300px;
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-fast);
    border: 2px solid #fff;
    display: block;
}

/* 视频全屏按钮样式 */
.video-fullscreen-overlay,
.video-fullscreen-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
}

.video-fullscreen-overlay:hover,
.video-fullscreen-btn:hover {
    background-color: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.video-fullscreen-overlay i,
.video-fullscreen-btn i {
    color: white;
    font-size: 16px;
}

/* 文件消息样式 */
.file-message {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 12px;
    padding: 0;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-fast);
}

.file-message:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.file-info {
    display: flex;
    align-items: center;
    padding: 16px;
    gap: 12px;
    position: relative;
}

.file-info.uploading {
    opacity: 0.7;
}

.file-info.uploading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-download {
    margin-left: auto;
    padding: 8px;
    border-radius: 50%;
    background: rgba(7, 193, 96, 0.1);
    transition: all var(--transition-fast);
}

.file-download:hover {
    background: rgba(7, 193, 96, 0.2);
    transform: scale(1.1);
}

.file-download a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 16px;
}

/* 上传中的媒体样式 */
.message-image.uploading,
.message-video.uploading {
    opacity: 0.7;
    position: relative;
}

.message-image.uploading::after,
.message-video.uploading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 消息状态样式 */
.message-status.uploading {
    color: #ffa500;
}

.message-status.uploading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.file-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    flex-shrink: 0;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.file-details {
    flex: 1;
    min-width: 0;
}

.file-name {
    font-size: 15px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.file-size {
    font-size: 13px;
    color: var(--text-secondary);
    opacity: 0.8;
}

/* 表情选择器 - 现代化设计 */
.emoji-picker {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.98) 100%);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-radius: 20px;
    box-shadow:
        0 -8px 32px rgba(0, 0, 0, 0.12),
        0 -4px 16px rgba(0, 0, 0, 0.08),
        0 -2px 8px rgba(0, 0, 0, 0.04);
    z-index: 1000;
    display: none;
    border: 2px solid transparent;
    margin-bottom: 8px;
    max-height: 350px;
    overflow: hidden;
}

.emoji-picker.active {
    display: block;
}

/* 表情选择器头部 - 隐藏分类按钮 */
.emoji-picker-header {
    display: none;
}

.emoji-categories {
    display: flex;
    gap: 8px;
    justify-content: space-around;
}

.emoji-category {
    width: 36px;
    height: 36px;
    border-radius: 12px;
    background: transparent;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    color: #666;
    transition: all 0.3s ease;
    position: relative;
}

.emoji-category:hover {
    background: rgba(7, 193, 96, 0.1);
    color: #07c160;
    transform: scale(1.1);
}

.emoji-category.active {
    background: linear-gradient(135deg, #07c160 0%, #4caf50 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(7, 193, 96, 0.3);
}

/* 表情内容区域 */
.emoji-picker-content {
    padding: 16px;
    display: grid;
    grid-template-columns: repeat(8, 1fr);
    gap: 8px;
    max-height: 240px;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(7, 193, 96, 0.3) transparent;
}

.emoji-picker-content::-webkit-scrollbar {
    width: 6px;
}

.emoji-picker-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

.emoji-picker-content::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #07c160 0%, #4caf50 100%);
    border-radius: 3px;
    transition: background 0.3s ease;
}

.emoji-picker-content::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #06a84d 0%, #43a047 100%);
}

.emoji-item {
    width: 36px;
    height: 36px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    cursor: pointer;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    user-select: none;
    position: relative;
    background: transparent;
}

.emoji-item::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
        rgba(255, 193, 7, 0.4) 0%,
        rgba(255, 152, 0, 0.4) 50%,
        rgba(156, 39, 176, 0.4) 100%);
    border-radius: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.emoji-item:hover::before {
    opacity: 1;
}

.emoji-item:hover {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    transform: translateY(-2px) scale(1.2);
    box-shadow:
        0 8px 20px rgba(243, 156, 18, 0.2),
        0 4px 12px rgba(0, 0, 0, 0.1),
        0 2px 6px rgba(0, 0, 0, 0.06);
}

.emoji-item:active {
    transform: translateY(0) scale(1.1);
    transition: all 0.1s ease;
}

/* 评论栏按钮样式 */
.comment-bar-actions {
    display: flex;
    align-items: center;
    margin-top: 8px;
}

.emoji-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 5px 10px;
    opacity: 0.7;
    transition: opacity 0.2s;
    border-radius: 4px;
}

.emoji-btn:hover {
    opacity: 1;
    background-color: #f5f5f5;
}

.send-btn, .cancel-btn {
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    margin-left: 8px;
}

.send-btn {
    background-color: #07c160;
    color: white;
    box-shadow: 0 2px 4px rgba(7, 193, 96, 0.3);
}

.send-btn:hover:not([disabled]) {
    background-color: #06b457;
    transform: translateY(-1px);
    box-shadow: 0 3px 6px rgba(7, 193, 96, 0.4);
}

.send-btn[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
}

.cancel-btn {
    background-color: #f5f5f5;
    color: #666;
    border: 1px solid #ddd;
}

.cancel-btn:hover {
    background-color: #eee;
    color: #333;
}

/* 媒体预览弹窗 */
.media-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99999;
    backdrop-filter: blur(4px);
    animation: modalFadeIn 0.3s ease;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.media-preview-content {
    max-width: 95%;
    max-height: 95%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.media-preview-content img,
.media-preview-content video {
    max-width: 100%;
    max-height: 90vh;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.5);
    object-fit: contain;
}

.media-close-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 100000;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
    color: white;
    font-size: 40px;
    font-weight: normal;
    line-height: 1;
}

.media-close-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}



.empty-message {
    text-align: center;
    padding: 30px;
    color: #999;
    font-size: 14px;
}

/* 聊天详情页面的表情选择器样式 */
.chat-detail .emoji-picker {
    position: absolute;
    bottom: 100%;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 249, 250, 0.98) 100%);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    border-radius: 20px;
    box-shadow:
        0 -8px 32px rgba(0, 0, 0, 0.12),
        0 -4px 16px rgba(0, 0, 0, 0.08),
        0 -2px 8px rgba(0, 0, 0, 0.04);
    z-index: 1000;
    display: none;
    border: 2px solid transparent;
    margin-bottom: 8px;
    max-height: 350px;
    overflow: hidden;
}

.chat-detail .emoji-picker.active {
    display: block !important;
}

/* 修复表情选择器定位问题 */
#emoji-picker.active,
.chat-detail .emoji-picker.active {
    display: block !important;
    /* 修复定位 */
    position: absolute !important;
    bottom: 100% !important;
    left: 0 !important;
    right: 0 !important;
    max-width: 100% !important;
    margin-bottom: 8px !important;
    z-index: 1000 !important;
}

/* 确保在所有情况下都隐藏表情分类头部 */
.emoji-picker-header,
.chat-detail .emoji-picker-header,
#emoji-picker .emoji-picker-header {
    display: none !important;
}

/* 聊天右键菜单样式 */
.chat-context-menu {
    position: absolute;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-radius: var(--border-radius-md);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    z-index: 10000;
    min-width: 140px;
    overflow: hidden;
    animation: contextMenuShow 0.2s ease-out;
}

.chat-context-menu-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: all var(--transition-fast);
    color: var(--text-primary);
    font-size: 14px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.chat-context-menu-item:last-child {
    border-bottom: none;
}

.chat-context-menu-item:hover {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.chat-context-menu-item i {
    margin-right: 8px;
    font-size: 14px;
    width: 16px;
    text-align: center;
}

@keyframes contextMenuShow {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-10px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* ==================== WebSocket 相关样式 ==================== */

/* 输入状态指示器 */
.typing-indicator {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    margin: 8px 0;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 18px;
    max-width: 200px;
    animation: fadeInUp 0.3s ease-out;
}

.typing-dots {
    display: flex;
    align-items: center;
    margin-right: 8px;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--primary-color);
    margin: 0 2px;
    animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
    animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
    animation-delay: -0.16s;
}

.typing-text {
    font-size: 12px;
    color: var(--text-muted);
    font-style: italic;
}

@keyframes typingDots {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* WebSocket连接状态指示器 */
.websocket-status {
    position: fixed;
    top: 10px;
    right: 10px;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    z-index: 1000;
    transition: all 0.3s ease;
}

.websocket-status.connected {
    background: rgba(76, 175, 80, 0.9);
    color: white;
}

.websocket-status.disconnected {
    background: rgba(244, 67, 54, 0.9);
    color: white;
}

.websocket-status.connecting {
    background: rgba(255, 193, 7, 0.9);
    color: white;
}

/* 在线状态指示器 */
.online-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
    background: #4CAF50;
    box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
}

.online-indicator.offline {
    background: #9E9E9E;
}

/* 消息状态增强样式 */
.message-status.read {
    color: #4CAF50;
}

.message-status.sent {
    color: #9E9E9E;
}

.message-status.sending {
    color: #FF9800;
}

.message-status.failed {
    color: #F44336;
    cursor: pointer;
}

/* 群聊消息已读状态样式 */
.message-status.group-read {
    color: #4CAF50;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
}

.message-status.group-read:hover {
    background: rgba(76, 175, 80, 0.1);
    border-radius: 4px;
    padding: 2px 4px;
}

/* 群聊已读详情弹窗样式 */
.group-read-modal {
    animation: fadeIn 0.3s ease;
}

.group-read-content {
    animation: slideUp 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 移动端聊天滑动删除样式 */
.chat-item.swipe-delete-ready-chat {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    box-shadow:
        0 4px 16px rgba(245, 158, 11, 0.2),
        0 2px 8px rgba(245, 158, 11, 0.1);
}

.swipe-delete-button-chat {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 90px;
    background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    border-radius: 0 16px 16px 0;
    z-index: 10;
    cursor: pointer;
    box-shadow:
        -2px 0 8px rgba(255, 71, 87, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.1);
    min-width: 90px;
    text-align: center;
    white-space: nowrap;
}

.swipe-delete-button-chat:hover {
    background: linear-gradient(135deg, #ff3742 0%, #ff2730 100%);
}

.swipe-delete-button-chat:active {
    transform: translateX(0) scale(0.95);
}

/* 触摸设备专用优化 */
@media (hover: none) and (pointer: coarse) {
    .chat-item {
        /* 为触摸设备优化触摸目标 */
        -webkit-tap-highlight-color: transparent;
        tap-highlight-color: transparent;
        user-select: none;
        -webkit-user-select: none;
        -webkit-touch-callout: none;
    }

    /* 禁用触摸设备上的hover效果 */
    .chat-item:hover {
        background: rgba(255, 255, 255, 0.95);
        transform: translateY(-2px) scale(1.02);
        box-shadow: var(--shadow-md);
    }

    .swipe-delete-button-chat:hover {
        background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
    }
}

/* 移动端响应式优化 */
@media (max-width: 480px) {
    .chat-item {
        /* 增加移动端的触摸目标尺寸 */
        min-height: 70px;
        padding: 16px 18px;
        margin: 6px 12px;
    }

    .swipe-delete-button-chat {
        width: 85px;
        min-width: 85px;
        font-size: 16px;
    }
}

/* 移动端聊天操作提示 */
.mobile-chat-tips {
    position: fixed;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 12px;
    z-index: 1000;
    animation: tipsFadeIn 0.3s ease-out;
    backdrop-filter: blur(10px);
}

.mobile-chat-tips .tips-content {
    display: flex;
    align-items: center;
    gap: 6px;
}

.mobile-chat-tips i {
    font-size: 14px;
    color: #fbbf24;
}

@keyframes tipsFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

@media (max-width: 480px) {
    .mobile-chat-tips {
        bottom: 70px;
        font-size: 11px;
        padding: 6px 12px;
    }
}


/* ===== 群聊功能样式 ===== */

/* 群聊按钮样式 */
.group-chat-btn {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, var(--primary-color) 0%, #06ae56 100%);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    box-shadow: var(--shadow-sm);
    font-size: 16px;
}

.group-chat-btn:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-md);
}

.group-chat-btn:active {
    transform: translateY(-50%) scale(0.95);
}

/* 聊天标题容器（支持群聊显示） */
.chat-title-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.chat-title-container h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
}

.member-count {
    font-size: 12px;
    color: var(--text-muted);
    margin-top: 2px;
}

/* 创建群聊页面样式 - 现代高端设计 */
.create-group-chat {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(135deg,
            rgba(99, 102, 241, 0.95) 0%,
            rgba(139, 92, 246, 0.95) 25%,
            rgba(236, 72, 153, 0.95) 50%,
            rgba(251, 146, 60, 0.95) 75%,
            rgba(34, 197, 94, 0.95) 100%),
        radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
    background-size: 400% 400%, 100% 100%, 100% 100%;
    animation: modernGradientFlow 20s ease infinite;
    z-index: 1000;
    transform: translateX(100%);
    transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-y: auto;
    overflow-x: hidden;
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* 现代化背景动画 */
@keyframes modernGradientFlow {
    0% {
        background-position: 0% 50%, 0% 0%, 0% 0%;
        filter: hue-rotate(0deg) brightness(1);
    }
    33% {
        background-position: 100% 50%, 20% 20%, 80% 80%;
        filter: hue-rotate(120deg) brightness(1.1);
    }
    66% {
        background-position: 50% 100%, 80% 80%, 20% 20%;
        filter: hue-rotate(240deg) brightness(0.9);
    }
    100% {
        background-position: 0% 50%, 0% 0%, 0% 0%;
        filter: hue-rotate(360deg) brightness(1);
    }
}

/* 现代化装饰层 */
.create-group-chat::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.03) 50%, transparent 70%),
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.12) 0%, transparent 40%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 0%, transparent 40%),
        conic-gradient(from 0deg at 50% 50%, transparent 0deg, rgba(255, 255, 255, 0.02) 90deg, transparent 180deg);
    pointer-events: none;
    z-index: 1;
    animation: decorativeFloat 25s ease-in-out infinite;
}

/* 添加微妙的粒子效果 */
.create-group-chat::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.08) 1px, transparent 1px),
        radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.06) 1px, transparent 1px);
    background-size: 50px 50px, 80px 80px, 120px 120px;
    pointer-events: none;
    z-index: 1;
    animation: particleFloat 30s linear infinite;
    opacity: 0.6;
}

@keyframes particleFloat {
    0% {
        background-position: 0% 0%, 0% 0%, 0% 0%;
    }
    100% {
        background-position: 100% 100%, -100% 100%, 50% -50%;
    }
}

@keyframes decorativeFloat {
    0%, 100% {
        opacity: 0.7;
        transform: translateY(0) rotate(0deg) scale(1);
    }
    33% {
        opacity: 0.9;
        transform: translateY(-8px) rotate(120deg) scale(1.02);
    }
    66% {
        opacity: 0.8;
        transform: translateY(5px) rotate(240deg) scale(0.98);
    }
}

.create-group-chat.active {
    transform: translateX(0);
    animation: pageSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes pageSlideIn {
    0% {
        transform: translateX(100%) scale(0.95);
        opacity: 0;
        filter: blur(10px);
    }
    50% {
        transform: translateX(-2%) scale(1.02);
        opacity: 0.8;
        filter: blur(2px);
    }
    100% {
        transform: translateX(0) scale(1);
        opacity: 1;
        filter: blur(0);
    }
}

.group-chat-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px; /* 减少上下padding */
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(30px);
    -webkit-backdrop-filter: blur(30px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.1),
        0 4px 16px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    flex-shrink: 0; /* 防止被压缩 */
}

.group-chat-header h3 {
    font-size: 20px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    position: relative;
}

/* 标题装饰 */
.group-chat-header h3::after {
    content: '✨';
    position: absolute;
    right: -25px;
    top: -2px;
    font-size: 16px;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.8; }
    50% { transform: scale(1.2) rotate(180deg); opacity: 1; }
}

.confirm-btn {
    background:
        linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%),
        radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    color: white;
    border: none;
    padding: 14px 28px;
    border-radius: 28px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 12px 35px rgba(99, 102, 241, 0.4),
        0 6px 15px rgba(139, 92, 246, 0.3),
        0 2px 8px rgba(236, 72, 153, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.confirm-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        rgba(255, 255, 255, 0.6),
        rgba(255, 255, 255, 0.4),
        transparent);
    transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.confirm-btn:hover::before {
    left: 100%;
}

.confirm-btn:disabled {
    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
    color: #94a3b8;
    cursor: not-allowed;
    box-shadow: none;
}

.confirm-btn:not(:disabled):hover {
    transform: translateY(-3px) scale(1.06);
    box-shadow:
        0 18px 45px rgba(99, 102, 241, 0.5),
        0 8px 25px rgba(139, 92, 246, 0.4),
        0 4px 12px rgba(236, 72, 153, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    filter: brightness(1.1) saturate(1.2);
}

.confirm-btn:not(:disabled):active {
    transform: translateY(0) scale(0.98);
}

/* 群聊信息区域 - 现代高端设计 */
.group-info-section {
    padding: 20px 28px;
    text-align: center;
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 255, 255, 0.92) 100%),
        radial-gradient(circle at 30% 30%, rgba(99, 102, 241, 0.05) 0%, transparent 70%);
    margin: 24px 24px 8px;
    border-radius: 32px;
    box-shadow:
        0 25px 70px rgba(0, 0, 0, 0.15),
        0 10px 30px rgba(0, 0, 0, 0.1),
        0 3px 12px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.95),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    backdrop-filter: blur(25px) saturate(1.2);
    -webkit-backdrop-filter: blur(25px) saturate(1.2);
    border: 1px solid rgba(255, 255, 255, 0.4);
    position: relative;
    z-index: 2;
    overflow: hidden;
    flex-shrink: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: gentleFloat 6s ease-in-out infinite;
}

@keyframes gentleFloat {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-2px) rotate(0.5deg);
    }
}

.group-info-section:hover {
    transform: translateY(-2px) scale(1.01);
    box-shadow:
        0 30px 80px rgba(0, 0, 0, 0.18),
        0 12px 35px rgba(0, 0, 0, 0.12),
        0 4px 15px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.98),
        inset 0 -1px 0 rgba(0, 0, 0, 0.03);
    border-color: rgba(255, 255, 255, 0.5);
}

/* 现代化装饰背景元素 */
.group-info-section::before {
    content: '';
    position: absolute;
    top: -30%;
    left: -30%;
    width: 160%;
    height: 160%;
    background:
        conic-gradient(from 45deg at 25% 25%, rgba(99, 102, 241, 0.08) 0deg, transparent 90deg, rgba(236, 72, 153, 0.06) 180deg, transparent 270deg),
        radial-gradient(circle at 70% 30%, rgba(139, 92, 246, 0.06) 0%, transparent 60%),
        radial-gradient(circle at 30% 70%, rgba(251, 146, 60, 0.05) 0%, transparent 60%);
    animation: modernFloat 8s ease-in-out infinite;
    pointer-events: none;
    opacity: 0.8;
}

.group-info-section::after {
    content: '';
    position: absolute;
    top: 10px;
    right: 10px;
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 50%;
    animation: sparkle 4s ease-in-out infinite;
    pointer-events: none;
}

@keyframes modernFloat {
    0%, 100% {
        transform: translate(0, 0) rotate(0deg) scale(1);
        opacity: 0.8;
    }
    33% {
        transform: translate(8px, -6px) rotate(2deg) scale(1.02);
        opacity: 0.9;
    }
    66% {
        transform: translate(-4px, 4px) rotate(-1deg) scale(0.98);
        opacity: 0.7;
    }
}

@keyframes sparkle {
    0%, 100% {
        opacity: 0.3;
        transform: scale(1) rotate(0deg);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.2) rotate(180deg);
    }
}

.group-avatar-container {
    position: relative;
    display: inline-block;
    margin-bottom: 24px;
}

.group-avatar-preview {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 48px;
    margin: 0 auto;
    box-shadow:
        0 20px 40px rgba(102, 126, 234, 0.3),
        0 8px 16px rgba(102, 126, 234, 0.2),
        inset 0 2px 0 rgba(255, 255, 255, 0.3);
    background-size: cover;
    background-position: center;
    border: 4px solid rgba(255, 255, 255, 0.8);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

/* 头像光晕效果 */
.group-avatar-preview::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    border-radius: 50%;
    z-index: -1;
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.group-avatar-preview:hover {
    transform: scale(1.05);
    box-shadow:
        0 25px 50px rgba(102, 126, 234, 0.4),
        0 12px 24px rgba(102, 126, 234, 0.3);
}

.change-avatar-btn {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: 3px solid white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 16px;
    color: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 8px 20px rgba(102, 126, 234, 0.3),
        0 4px 8px rgba(102, 126, 234, 0.2);
}

.change-avatar-btn:hover {
    transform: scale(1.15) rotate(15deg);
    box-shadow:
        0 12px 30px rgba(102, 126, 234, 0.4),
        0 6px 12px rgba(102, 126, 234, 0.3);
}

.change-avatar-btn:active {
    transform: scale(0.95) rotate(0deg);
}

.group-name-input-container {
    position: relative;
    margin-top: 8px;
}

.group-name-input-container input {
    width: 100%;
    padding: 18px 55px 18px 24px;
    border: 2px solid rgba(99, 102, 241, 0.2);
    border-radius: 30px;
    font-size: 18px;
    text-align: center;
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%),
        radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.03) 0%, transparent 70%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    color: #2d3748;
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.08),
        0 3px 10px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.group-name-input-container input::placeholder {
    color: #a0aec0;
    font-weight: 400;
}

.group-name-input-container input:focus {
    border-color: rgba(99, 102, 241, 0.6);
    box-shadow:
        0 0 0 4px rgba(99, 102, 241, 0.15),
        0 12px 35px rgba(99, 102, 241, 0.25),
        0 6px 15px rgba(139, 92, 246, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    outline: none;
    transform: translateY(-3px) scale(1.02);
    filter: brightness(1.05);
}

.input-counter {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 13px;
    color: #a0aec0;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.8);
    padding: 4px 8px;
    border-radius: 12px;
    backdrop-filter: blur(10px);
}

/* 成员选择区域 - 现代高端设计 */
/* 已选成员区域样式 - 独立区域 */
.selected-members-section {
    flex: 0 0 auto;
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.97) 0%, rgba(255, 255, 255, 0.93) 100%),
        radial-gradient(circle at 20% 20%, rgba(139, 92, 246, 0.04) 0%, transparent 70%);
    margin: 6px 24px 18px;
    border-radius: 30px;
    box-shadow:
        0 18px 50px rgba(0, 0, 0, 0.12),
        0 6px 20px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    backdrop-filter: blur(22px) saturate(1.1);
    -webkit-backdrop-filter: blur(22px) saturate(1.1);
    border: 1px solid rgba(255, 255, 255, 0.35);
    overflow: visible; /* 修复：改为visible确保内容不被隐藏 */
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    max-height: 200px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: gentleFloat 8s ease-in-out infinite;
    animation-delay: 0.5s;
}

.selected-members-section:hover {
    transform: translateY(-1px);
    box-shadow:
        0 22px 60px rgba(0, 0, 0, 0.15),
        0 8px 25px rgba(0, 0, 0, 0.1),
        0 3px 10px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.95),
        inset 0 -1px 0 rgba(0, 0, 0, 0.03);
    border-color: rgba(255, 255, 255, 0.45);
}

/* 好友列表区域样式 - 现代高端设计 */
.friends-list-section {
    flex: 1;
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.97) 0%, rgba(255, 255, 255, 0.93) 100%),
        radial-gradient(circle at 80% 80%, rgba(236, 72, 153, 0.04) 0%, transparent 70%);
    margin: 10px 24px 16px;
    border-radius: 30px;
    box-shadow:
        0 20px 55px rgba(0, 0, 0, 0.12),
        0 8px 22px rgba(0, 0, 0, 0.08),
        0 3px 10px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9),
        inset 0 -1px 0 rgba(0, 0, 0, 0.02);
    backdrop-filter: blur(22px) saturate(1.1);
    -webkit-backdrop-filter: blur(22px) saturate(1.1);
    border: 1px solid rgba(255, 255, 255, 0.35);
    overflow: hidden;
    position: relative;
    z-index: 3;
    display: flex;
    flex-direction: column;
    min-height: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    animation: gentleFloat 10s ease-in-out infinite;
    animation-delay: 1s;
}

.friends-list-section:hover {
    transform: translateY(-1px);
    box-shadow:
        0 25px 65px rgba(0, 0, 0, 0.15),
        0 10px 28px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.95),
        inset 0 -1px 0 rgba(0, 0, 0, 0.03);
    border-color: rgba(255, 255, 255, 0.45);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 24px 6px; /* 进一步减少上下padding */
    border-bottom: 2px solid rgba(102, 126, 234, 0.1);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(245, 87, 108, 0.02) 100%);
    position: relative; /* 确保正常的相对定位 */
    z-index: 1; /* 确保标题在上层显示 */
}

.section-header h4 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: static; /* 修改为static定位，避免定位问题 */
    display: inline-block; /* 确保正常的行内块显示 */
}

.section-header h4::after {
    content: '👥';
    position: relative; /* 修改为relative定位 */
    margin-left: 8px; /* 使用margin而不是绝对定位 */
    font-size: 16px;
    animation: bounce 2s ease-in-out infinite;
    display: inline-block; /* 确保表情符号正常显示 */
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-8px); }
    60% { transform: translateY(-4px); }
}

.selected-count {
    font-size: 15px;
    color: #667eea;
    font-weight: 600;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%);
    padding: 8px 16px;
    border-radius: 20px;
    border: 2px solid rgba(102, 126, 234, 0.2);
    backdrop-filter: blur(10px);
}

.selected-members {
    padding: 12px 20px 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    min-height: 60px; /* 增加最小高度确保内容可见 */
    max-height: 200px; /* 增加最大高度给更多显示空间 */
    overflow-y: auto;
    overflow-x: visible; /* 确保水平方向不被截断 */
    align-items: flex-start;
    background:
        linear-gradient(135deg, rgba(99, 102, 241, 0.03) 0%, rgba(236, 72, 153, 0.03) 100%),
        radial-gradient(circle at 20% 20%, rgba(139, 92, 246, 0.02) 0%, transparent 70%);
    flex-shrink: 0;
    position: relative;
    z-index: 2; /* 提高z-index确保在最上层 */
    border-radius: 16px; /* 添加圆角 */
    border: 1px solid rgba(139, 92, 246, 0.1); /* 添加边框便于调试 */
}

.selected-member {
    display: flex;
    align-items: center;
    background:
        linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%),
        radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    color: white;
    padding: 10px 16px;
    border-radius: 24px;
    font-size: 14px; /* 增加字体大小提高可读性 */
    font-weight: 600;
    animation: slideInBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    box-shadow:
        0 8px 20px rgba(99, 102, 241, 0.3),
        0 4px 10px rgba(139, 92, 246, 0.2),
        0 2px 6px rgba(236, 72, 153, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: visible; /* 修复：确保内容不被截断 */
    z-index: 3; /* 确保在最上层 */
    margin: 2px; /* 添加外边距避免重叠 */
    flex-shrink: 0; /* 防止被压缩 */
    white-space: nowrap; /* 防止文字换行 */
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 1;
}

.selected-member::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        rgba(255, 255, 255, 0.4),
        rgba(255, 255, 255, 0.3),
        transparent);
    transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 0;
}

.selected-member:hover::before {
    left: 100%;
}

@keyframes slideInBounce {
    0% {
        transform: translateX(-100px) scale(0.5);
        opacity: 0;
    }
    50% {
        transform: translateX(10px) scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

.selected-member:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow:
        0 12px 30px rgba(99, 102, 241, 0.4),
        0 6px 15px rgba(139, 92, 246, 0.3),
        0 3px 8px rgba(236, 72, 153, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
    filter: brightness(1.05) saturate(1.1);
}

.selected-member > * {
    position: relative;
    z-index: 3;
}

.selected-member .remove-btn {
    margin-left: 12px;
    cursor: pointer;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.2) 100%),
        radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    z-index: 4;
}

.selected-member .remove-btn:hover {
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.8) 100%),
        radial-gradient(circle at 30% 30%, rgba(236, 72, 153, 0.1) 0%, transparent 70%);
    color: #ec4899;
    transform: scale(1.15) rotate(90deg);
    box-shadow:
        0 4px 12px rgba(255, 255, 255, 0.4),
        0 2px 6px rgba(236, 72, 153, 0.2);
    border-color: rgba(236, 72, 153, 0.3);
}

/* 空状态提示样式 */
.empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    color: #a0aec0;
    font-size: 14px;
    font-weight: 500;
    gap: 8px;
    padding: 8px 0;
    opacity: 0.8;
}

.friends-list-container {
    flex: 1; /* 占用剩余空间 */
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(102, 126, 234, 0.3) transparent;
    min-height: 0; /* 允许收缩 */
    position: relative; /* 确保正常的相对定位 */
    z-index: 0; /* 确保好友列表在标题下方 */
}

.friends-list-container::-webkit-scrollbar {
    width: 6px;
}

.friends-list-container::-webkit-scrollbar-track {
    background: rgba(102, 126, 234, 0.05);
    border-radius: 3px;
}

.friends-list-container::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 3px;
}

.friends-list-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
}

.search-container {
    position: relative;
    padding: 16px 24px; /* 增加上下边距，因为现在是独立区域的顶部 */
    border-bottom: 2px solid rgba(102, 126, 234, 0.1);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(245, 87, 108, 0.05) 100%);
    flex-shrink: 0; /* 防止被压缩 */
}

.search-container input {
    width: 100%;
    padding: 16px 55px 16px 24px;
    border: 2px solid rgba(99, 102, 241, 0.25);
    border-radius: 28px;
    font-size: 16px;
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.97) 0%, rgba(255, 255, 255, 0.93) 100%),
        radial-gradient(circle at 30% 30%, rgba(139, 92, 246, 0.03) 0%, transparent 70%);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 500;
    color: #2d3748;
    backdrop-filter: blur(15px) saturate(1.1);
    -webkit-backdrop-filter: blur(15px) saturate(1.1);
    box-shadow:
        0 6px 20px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.search-container input::placeholder {
    color: #a0aec0;
    font-weight: 400;
}

.search-container input:focus {
    border-color: rgba(99, 102, 241, 0.6);
    box-shadow:
        0 0 0 4px rgba(99, 102, 241, 0.15),
        0 10px 30px rgba(99, 102, 241, 0.2),
        0 4px 12px rgba(139, 92, 246, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    outline: none;
    transform: translateY(-2px) scale(1.01);
    filter: brightness(1.05);
}

.search-container i {
    position: absolute;
    right: 40px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    font-size: 18px;
    transition: all 0.3s ease;
}

.search-container input:focus + i {
    color: #667eea;
    transform: translateY(-50%) scale(1.1);
}

.friend-item {
    display: flex;
    align-items: center;
    padding: 16px 24px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-bottom: 1px solid rgba(102, 126, 234, 0.08);
    position: relative;
    overflow: hidden;
}

.friend-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%);
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1;
}

.friend-item:hover::before {
    width: 100%;
}

.friend-item:hover {
    background:
        linear-gradient(145deg, rgba(99, 102, 241, 0.04) 0%, rgba(139, 92, 246, 0.03) 100%),
        radial-gradient(circle at 20% 20%, rgba(236, 72, 153, 0.02) 0%, transparent 70%);
    transform: translateX(10px) translateY(-1px);
    box-shadow:
        0 12px 35px rgba(99, 102, 241, 0.12),
        0 6px 15px rgba(139, 92, 246, 0.08),
        0 2px 8px rgba(236, 72, 153, 0.05);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

.friend-item.selected {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(245, 87, 108, 0.1) 100%);
    border-left: 4px solid #667eea;
    transform: translateX(8px);
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.15),
        0 4px 12px rgba(102, 126, 234, 0.1);
}

.friend-item.selected::before {
    width: 100%;
}

.friend-item .friend-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    margin-right: 16px;
    background-size: cover;
    background-position: center;
    border: 3px solid rgba(255, 255, 255, 0.8);
    box-shadow:
        0 8px 20px rgba(102, 126, 234, 0.2),
        0 4px 8px rgba(102, 126, 234, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 2;
}

.friend-item:hover .friend-avatar {
    transform: scale(1.12) rotate(2deg);
    box-shadow:
        0 15px 40px rgba(99, 102, 241, 0.35),
        0 8px 20px rgba(139, 92, 246, 0.25),
        0 3px 10px rgba(236, 72, 153, 0.15);
    filter: brightness(1.1) saturate(1.2);
}

.friend-item .friend-info {
    flex: 1;
    position: relative;
    z-index: 2;
}

.friend-item .friend-name {
    font-size: 17px;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 4px;
    transition: all 0.3s ease;
}

.friend-item:hover .friend-name {
    color: #6366f1;
    transform: translateX(3px);
    text-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.friend-item .friend-status {
    font-size: 13px;
    color: #a0aec0;
    font-weight: 500;
}

.friend-item .selection-indicator {
    width: 24px;
    height: 24px;
    border: 3px solid rgba(102, 126, 234, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    z-index: 2;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
}

.friend-item:hover .selection-indicator {
    border-color: #6366f1;
    transform: scale(1.15) rotate(5deg);
    box-shadow:
        0 6px 20px rgba(99, 102, 241, 0.3),
        0 2px 8px rgba(139, 92, 246, 0.2);
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%),
        radial-gradient(circle at 30% 30%, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
}

.friend-item.selected .selection-indicator {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
    transform: scale(1.1);
    box-shadow:
        0 8px 20px rgba(102, 126, 234, 0.3),
        0 4px 8px rgba(102, 126, 234, 0.2);
}

.friend-item.selected .selection-indicator::before {
    content: '✓';
    font-size: 14px;
    font-weight: 700;
}

/* 无好友提示样式 - 可爱卡通风格 */
.no-friends-message {
    text-align: center;
    padding: 60px 24px;
    color: #a0aec0;
    font-size: 16px;
    font-weight: 500;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(245, 87, 108, 0.05) 100%);
    border-radius: 24px;
    margin: 24px;
    border: 2px dashed rgba(102, 126, 234, 0.2);
    position: relative;
    overflow: hidden;
}

.no-friends-message::before {
    content: '🥺';
    display: block;
    font-size: 48px;
    margin-bottom: 16px;
    animation: wiggle 2s ease-in-out infinite;
}

@keyframes wiggle {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-5deg); }
    75% { transform: rotate(5deg); }
}

.no-friends-message::after {
    content: '暂时还没有小伙伴可以邀请哦~';
    display: block;
    font-size: 14px;
    color: #cbd5e1;
    margin-top: 8px;
    font-weight: 400;
}

/* 群聊详情页面样式 - 卖萌风格 */
.group-chat-detail {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-attachment: fixed;
    z-index: 1000;
    transform: translateX(100%);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow-y: auto;
    overflow-x: hidden;
}

.group-chat-detail.active {
    transform: translateX(0);
}

.group-detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 1px solid rgba(255, 255, 255, 0.6); /* 更柔和的边框 */
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05); /* 减少阴影强度 */
}

.group-detail-header h3 {
    font-size: 20px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    position: relative;
}

.group-detail-header h3::after {
    content: '✨';
    position: absolute;
    right: -25px;
    top: -5px;
    font-size: 16px;
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1) rotate(0deg);
    }
    50% {
        opacity: 1;
        transform: scale(1.2) rotate(180deg);
    }
}

/* 浮动装饰元素 */
.group-chat-detail::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

.group-more-btn {
    width: 40px;
    height: 40px;
    border: none;
    background: transparent;
    color: var(--text-primary);
    font-size: 18px;
    cursor: pointer;
    border-radius: 50%;
    transition: all var(--transition-fast);
    display: none; /* 隐藏群聊详情右上角的3个点 */
}

.group-more-btn:hover {
    background: rgba(0, 0, 0, 0.05);
}

/* 群聊信息卡片 - 卖萌风格 */
.group-info-card {
    display: flex;
    align-items: center;
    padding: 30px 25px;
    background: rgba(255, 255, 255, 0.95);
    margin: 20px;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05); /* 减少阴影强度 */
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.6); /* 更柔和的边框 */
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.group-info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.group-info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.group-avatar-large {
    width: 70px;
    height: 70px;
    border-radius: var(--border-radius-md);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 28px;
    margin-right: 20px;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
    background-size: cover;
    background-position: center;
    position: relative;
    transition: all 0.3s ease;
}

/* 当显示九宫格时，使用圆角矩形而不是圆形 */
.group-avatar-large:has(.group-avatar-grid) {
    border-radius: var(--border-radius-md);
}

/* 群详情大头像的九宫格尺寸调整 */
.group-avatar-large .group-avatar-grid.large {
    width: 100%;
    height: 100%;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, 1fr);
    gap: 2px;
    border-radius: inherit;
    overflow: hidden;
}

}

.group-avatar-large::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
    z-index: -1;
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.group-info-card:hover .group-avatar-large {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.group-basic-info h4 {
    margin: 0 0 8px 0;
    font-size: 20px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.group-basic-info h4::after {
    content: '👥';
    position: absolute;
    right: -25px;
    top: 0;
    font-size: 16px;
    animation: bounce 2s ease-in-out infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

.group-member-count {
    font-size: 14px;
    color: #8b5cf6;
    font-weight: 500;
    background: rgba(139, 92, 246, 0.1);
    padding: 4px 12px;
    border-radius: 12px;
    display: inline-block;
}

/* 群成员区域 - 卖萌风格 */
.group-members-section {
    background: rgba(255, 255, 255, 0.95);
    margin: 0 20px 20px;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05); /* 减少阴影强度 */
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.6); /* 更柔和的边框 */
    overflow: hidden;
    position: relative;
}

.group-members-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid rgba(139, 92, 246, 0.05); /* 更柔和的边框 */
    background: rgba(139, 92, 246, 0.01); /* 更淡的背景 */
}

.section-title h5 {
    margin: 0;
    font-size: 18px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.section-title h5::after {
    content: '👫';
    position: absolute;
    right: -25px;
    top: 0;
    font-size: 16px;
    animation: wiggle 2s ease-in-out infinite;
}

@keyframes wiggle {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(-5deg); }
    75% { transform: rotate(5deg); }
}

.add-member-btn {
    width: 36px;
    height: 36px;
    border: 2px solid transparent;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    font-size: 16px;
    position: relative;
    overflow: hidden;
}

.add-member-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.add-member-btn:hover {
    transform: scale(1.1) rotate(180deg);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.add-member-btn:hover::before {
    left: 100%;
}

.group-members-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
    gap: 20px;
    padding: 25px;
}

.member-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    padding: 12px;
    border-radius: 16px;
    position: relative;
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.member-item:hover {
    background: rgba(139, 92, 246, 0.1);
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.2);
}

.member-item .member-avatar {
    width: 55px;
    height: 55px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    margin-bottom: 10px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    background-size: cover;
    background-position: center;
    position: relative;
    transition: all 0.3s ease;
}

.member-item:hover .member-avatar {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

/* 确保有头像的成员在悬停时保持头像显示 */
.member-item:hover .member-avatar[style*="background-image"] {
    color: transparent !important; /* 隐藏文字 */
}

/* 确保有头像的成员始终保持头像显示，不被蓝色背景覆盖 */
.member-avatar[style*="background-image"] {
    background-color: transparent !important; /* 只移除背景色，保留背景图片 */
    background-size: cover !important; /* 确保头像覆盖整个区域 */
    background-position: center !important; /* 居中显示 */
    background-repeat: no-repeat !important; /* 不重复 */
}

/* 确保群聊头像网格中有头像的元素正确显示 */
.group-avatar-grid .group-avatar-cell[style*="background-image"] {
    color: transparent !important; /* 隐藏文字 */
    background-color: transparent !important; /* 只移除背景色，保留背景图片 */
    background-size: cover !important; /* 确保头像覆盖整个区域 */
    background-position: center !important; /* 居中显示 */
    background-repeat: no-repeat !important; /* 不重复 */
}

.member-item .member-avatar::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.member-item:hover .member-avatar::after {
    opacity: 1;
    animation: rotate 2s linear infinite;
}

/* 禁用有头像的成员在悬浮时显示蓝色圈圈，但保留头像本身的旋转动画 */
.member-item:hover .member-avatar[style*="background-image"]::after {
    opacity: 0 !important;
}

/* 有头像的成员悬浮时头像本身旋转 */
.member-item:hover .member-avatar[style*="background-image"] {
    animation: rotate 4s linear infinite;
}

.member-item .member-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 12px;
    width: 100%;
}

.member-item .member-name {
    font-size: 13px;
    color: #4c1d95;
    font-weight: 600;
    max-width: 70px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.member-item .owner-badge {
    font-size: 10px;
    background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
    color: white;
    padding: 2px 6px;
    border-radius: 8px;
    margin-top: 4px;
    font-weight: 600;
}

/* 移除群员按钮样式 */
.member-item .remove-member-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    width: 24px;
    height: 24px;
    border: none;
    background: rgba(239, 68, 68, 0.9);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.3s ease;
    opacity: 0;
    transform: scale(0.8);
    z-index: 10;
}

.member-item:hover .remove-member-btn {
    opacity: 1;
    transform: scale(1);
}

.member-item .remove-member-btn:hover {
    background: rgba(239, 68, 68, 1);
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4);
}

/* 群聊操作区域 - 卖萌风格 */
.group-actions-section {
    background: rgba(255, 255, 255, 0.95);
    margin: 0 20px 20px;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05); /* 减少阴影强度 */
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.6); /* 更柔和的边框 */
    overflow: hidden;
    position: relative;
}

.group-actions-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
}

.action-item {
    display: flex;
    align-items: center;
    padding: 18px 20px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-bottom: 1px solid rgba(139, 92, 246, 0.03); /* 更柔和的边框 */
    position: relative;
}

.action-item:last-child {
    border-bottom: none;
}

.action-item:hover {
    background: rgba(139, 92, 246, 0.05);
    transform: translateX(5px);
}

.action-item.danger-action:hover {
    background: rgba(239, 68, 68, 0.05);
    transform: translateX(5px);
}

.action-icon {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
    color: white;
    font-size: 18px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.action-item:hover .action-icon {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.danger-action .action-icon {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.action-item:hover.danger-action .action-icon {
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

.action-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.action-title {
    font-size: 16px;
    font-weight: 600;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.danger-action .action-title {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.action-content i {
    color: #8b5cf6;
    font-size: 16px;
}

/* 切换开关样式 - 卖萌风格 */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 28px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-switch label {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 14px;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toggle-switch label:before {
    position: absolute;
    content: "";
    height: 24px;
    width: 24px;
    left: 2px;
    bottom: 2px;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.toggle-switch input:checked + label {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.toggle-switch input:checked + label:before {
    transform: translateX(22px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.toggle-switch:hover label {
    transform: scale(1.05);
}

.toggle-switch input:checked + label::after {
    content: '✨';
    position: absolute;
    right: 6px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 12px;
    color: white;
    animation: sparkle 1s ease-in-out infinite;
}

/* 动画效果 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 群聊特殊样式标识 */
/* 移除群聊头像绿色边框以保持界面样式统一 */
/* .chat-item.group-chat .chat-avatar {
    border: 2px solid var(--primary-color);
} */

.chat-item.group-chat .chat-name::after {
    content: '👥';
    margin-left: 6px;
    font-size: 12px;
}

/* 置顶聊天样式 - 仅适用于非群聊 */
.chat-item.pinned-chat:not(.group-chat) {
    background: linear-gradient(135deg, rgba(7, 193, 96, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-left: 4px solid var(--primary-color);
}

.chat-item.pinned-chat:not(.group-chat):hover {
    background: linear-gradient(135deg, rgba(7, 193, 96, 0.15) 0%, rgba(255, 255, 255, 0.95) 100%);
}

/* 群聊置顶样式 - 移除绿色边框，使用不同的视觉标识 */
.chat-item.group-chat.pinned-chat {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(255, 255, 255, 0.9) 100%) !important;
    border-left: none !important;
    position: relative;
}

.chat-item.group-chat.pinned-chat:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(255, 255, 255, 0.95) 100%) !important;
}

/* 为群聊置顶添加特殊的视觉标识 - 已移除红色图标 */
/* .chat-item.group-chat.pinned-chat::before {
    content: '📌';
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 12px;
    opacity: 0.7;
} */

/* 免打扰聊天样式 */
.chat-item.muted-chat .chat-message {
    opacity: 0.7;
}

.chat-item.muted-chat .chat-badge {
    background: #94a3b8;
}

/* 移动端适配 */
@media (max-width: 480px) {
    .group-chat-btn {
        width: 36px;
        height: 36px;
        font-size: 14px;
        right: 16px;
    }

    .group-info-section {
        margin: 15px 15px 4px; /* 进一步减少底部边距 */
        padding: 12px 15px; /* 进一步减少上下padding */
    }

    .selected-members-section {
        margin: 4px 15px 16px; /* 增加下边距，避免与好友列表重叠 */
        max-height: 160px; /* 移动端限制最大高度，防止覆盖下方内容 */
    }

    .friends-list-section {
        margin: 8px 15px 15px; /* 增加上边距，确保与选择成员区域有足够间距 */
        max-height: calc(100vh - 220px); /* 相应调整最大高度以适应增加的间距 */
    }

    .group-info-card {
        margin: 15px;
        padding: 20px 15px;
    }

    .group-members-section {
        margin: 0 15px 15px;
    }

    .group-actions-section {
        margin: 0 15px 15px;
    }

    .group-members-grid {
        grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
        gap: 12px;
        padding: 15px;
    }

    .member-item .member-avatar {
        width: 45px;
        height: 45px;
    }

    /* 移动端创建群聊页面优化 */
    .create-group-chat {
        padding-bottom: 20px;
    }

    .group-chat-header {
        padding: 15px;
    }

    .group-avatar-preview {
        width: 70px;
        height: 70px;
        font-size: 28px;
    }

    .change-avatar-btn {
        width: 24px;
        height: 24px;
        font-size: 10px;
        bottom: -3px;
        right: -3px;
    }

    .section-header {
        padding: 6px 15px 4px; /* 移动端进一步减少section-header的padding */
    }

    .selected-members {
        padding: 6px 15px 10px; /* 进一步减少上下padding */
        min-height: 35px; /* 进一步减少移动端最小高度 */
        max-height: 100px; /* 适当增加移动端最大高度，确保能看到选中的好友 */
        overflow-y: auto; /* 超出时显示滚动条 */
    }

    .selected-member {
        padding: 5px 10px;
        font-size: 11px;
    }

    .selected-member .remove-btn {
        width: 14px;
        height: 14px;
        font-size: 9px;
        margin-left: 6px;
    }

    .friend-item {
        padding: 10px 15px;
    }

    .friend-item .friend-avatar {
        width: 35px;
        height: 35px;
        margin-right: 10px;
    }

    .friend-item .friend-name {
        font-size: 15px;
    }

    .friend-item .friend-status {
        font-size: 11px;
    }

    .friend-item .selection-indicator {
        width: 18px;
        height: 18px;
    }

    /* 群聊详情页面移动端优化 */
    .group-detail-header {
        padding: 15px;
    }

    .group-avatar-large {
        width: 55px;
        height: 55px;
        font-size: 22px;
        margin-right: 12px;
    }

    .group-basic-info h4 {
        font-size: 16px;
    }

    .group-member-count {
        font-size: 13px;
    }

    .action-item {
        padding: 14px 15px;
    }

    .action-icon {
        width: 36px;
        height: 36px;
        margin-right: 12px;
    }

    .action-title {
        font-size: 15px;
    }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
    .group-members-grid {
        grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
        gap: 10px;
        padding: 12px;
    }

    .member-item .member-avatar {
        width: 40px;
        height: 40px;
    }

    .member-item .member-name {
        font-size: 11px;
        max-width: 60px;
    }

    .selected-members {
        gap: 6px;
        min-height: 28px; /* 大幅减少超小屏幕最小高度 */
    }

    .selected-member {
        padding: 4px 8px;
        font-size: 10px;
    }
}

/* 平板设备适配 */
@media (min-width: 768px) and (max-width: 1024px) {
    .create-group-chat {
        max-width: 600px;
        margin: 0 auto;
        box-shadow: var(--shadow-lg);
        border-radius: var(--border-radius-lg);
        overflow: hidden;
    }

    .group-chat-detail {
        max-width: 600px;
        margin: 0 auto;
        box-shadow: var(--shadow-lg);
        border-radius: var(--border-radius-lg);
        overflow: hidden;
    }

    .group-members-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 20px;
        padding: 25px;
    }

    .member-item .member-avatar {
        width: 60px;
        height: 60px;
    }

    .member-item .member-name {
        font-size: 14px;
        max-width: 90px;
    }
}

/* 桌面设备适配 */
@media (min-width: 1025px) {
    .create-group-chat,
    .group-chat-detail {
        max-width: 700px;
        margin: 0 auto;
        box-shadow: var(--shadow-xl);
        border-radius: var(--border-radius-lg);
        overflow: hidden;
        top: 5%;
        height: 90%;
    }

    .group-members-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 24px;
        padding: 30px;
    }

    .member-item .member-avatar {
        width: 70px;
        height: 70px;
    }

    .member-item .member-name {
        font-size: 15px;
        max-width: 100px;
    }

    .friends-list-container {
        flex: 1;
        min-height: 0;
    }

    .group-info-section {
        padding: 40px 30px;
    }

    .group-avatar-preview {
        width: 100px;
        height: 100px;
        font-size: 40px;
    }

    .change-avatar-btn {
        width: 32px;
        height: 32px;
        font-size: 14px;
        bottom: -6px;
        right: -6px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .group-chat-btn {
        border: 2px solid white;
    }

    .friend-item.selected {
        background: #000;
        color: #fff;
    }

    .selected-member {
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .action-item {
        border-bottom: 2px solid #ddd;
    }
}

/* 减弱动效模式支持 */
@media (prefers-reduced-motion: reduce) {
    .create-group-chat,
    .group-chat-detail,
    .add-member-page {
        transition: none;
    }

    .selected-member {
        animation: none;
    }

    .member-item:hover {
        transform: none;
    }

    .group-chat-btn:hover {
        transform: translateY(-50%);
    }

    .confirm-btn:not(:disabled):hover {
        transform: none;
    }

    .add-member-btn:hover {
        transform: none;
    }
}

/* 添加成员页面样式 - 现代奢华设计 */
.add-member-page {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(135deg,
            rgba(79, 172, 254, 0.95) 0%,
            rgba(139, 92, 246, 0.95) 30%,
            rgba(236, 72, 153, 0.95) 60%,
            rgba(251, 146, 60, 0.95) 100%),
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.08) 0%, transparent 50%);
    background-size: 400% 400%, 100% 100%, 100% 100%;
    animation: luxuryGradientFlow 25s ease infinite;
    z-index: 1100;
    transform: translateX(100%);
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
}

/* 奢华装饰层 */
.add-member-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        conic-gradient(from 0deg at 30% 30%, rgba(255, 255, 255, 0.12) 0deg, transparent 120deg, rgba(255, 255, 255, 0.08) 240deg, transparent 360deg),
        radial-gradient(circle at 70% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 60%),
        radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.08) 0%, transparent 60%),
        linear-gradient(45deg, transparent 40%, rgba(255, 255, 255, 0.02) 50%, transparent 60%);
    pointer-events: none;
    animation: luxuryFloat 22s infinite ease-in-out;
    opacity: 0.9;
}

/* 奢华粒子效果 */
.add-member-page::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
        radial-gradient(circle at 15% 15%, rgba(255, 255, 255, 0.12) 1px, transparent 1px),
        radial-gradient(circle at 85% 85%, rgba(255, 255, 255, 0.1) 1px, transparent 1px),
        radial-gradient(circle at 50% 30%, rgba(255, 255, 255, 0.08) 1px, transparent 1px),
        radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.06) 1px, transparent 1px);
    background-size: 60px 60px, 90px 90px, 130px 130px, 170px 170px;
    pointer-events: none;
    z-index: 1;
    animation: luxuryParticleFloat 35s linear infinite;
    opacity: 0.7;
}

@keyframes luxuryParticleFloat {
    0% {
        background-position: 0% 0%, 0% 0%, 0% 0%, 0% 0%;
        transform: rotate(0deg);
    }
    25% {
        background-position: 25% 25%, -25% 25%, 50% -25%, 75% 50%;
        transform: rotate(90deg);
    }
    50% {
        background-position: 50% 50%, -50% 50%, 100% -50%, 150% 100%;
        transform: rotate(180deg);
    }
    75% {
        background-position: 75% 75%, -75% 75%, 150% -75%, 225% 150%;
        transform: rotate(270deg);
    }
    100% {
        background-position: 100% 100%, -100% 100%, 200% -100%, 300% 200%;
        transform: rotate(360deg);
    }
}

/* 奢华动画效果 */
@keyframes luxuryGradientFlow {
    0% {
        background-position: 0% 50%, 0% 0%, 0% 0%;
        filter: hue-rotate(0deg) brightness(1) saturate(1);
    }
    25% {
        background-position: 50% 25%, 25% 25%, 75% 75%;
        filter: hue-rotate(90deg) brightness(1.05) saturate(1.1);
    }
    50% {
        background-position: 100% 50%, 50% 50%, 50% 50%;
        filter: hue-rotate(180deg) brightness(0.95) saturate(1.2);
    }
    75% {
        background-position: 50% 75%, 75% 75%, 25% 25%;
        filter: hue-rotate(270deg) brightness(1.02) saturate(1.05);
    }
    100% {
        background-position: 0% 50%, 0% 0%, 0% 0%;
        filter: hue-rotate(360deg) brightness(1) saturate(1);
    }
}

@keyframes luxuryFloat {
    0%, 100% {
        opacity: 0.9;
        transform: translateY(0) rotate(0deg) scale(1);
    }
    25% {
        opacity: 1;
        transform: translateY(-12px) rotate(90deg) scale(1.03);
    }
    50% {
        opacity: 0.8;
        transform: translateY(8px) rotate(180deg) scale(0.97);
    }
    75% {
        opacity: 0.95;
        transform: translateY(-6px) rotate(270deg) scale(1.01);
    }
}

.add-member-page.active {
    transform: translateX(0);
    animation: luxuryPageSlideIn 1s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes luxuryPageSlideIn {
    0% {
        transform: translateX(100%) scale(0.9) rotateY(15deg);
        opacity: 0;
        filter: blur(15px) brightness(0.8);
    }
    30% {
        transform: translateX(5%) scale(1.05) rotateY(-2deg);
        opacity: 0.7;
        filter: blur(5px) brightness(1.1);
    }
    70% {
        transform: translateX(-2%) scale(0.98) rotateY(1deg);
        opacity: 0.9;
        filter: blur(1px) brightness(1.05);
    }
    100% {
        transform: translateX(0) scale(1) rotateY(0deg);
        opacity: 1;
        filter: blur(0) brightness(1);
    }
}

.add-member-page.active::before {
    animation: luxuryFloat 22s infinite ease-in-out, pageEnterLuxury 1s ease-out;
}

@keyframes pageEnterLuxury {
    0% {
        opacity: 0;
        transform: scale(0.8) rotate(-10deg);
        filter: blur(10px);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05) rotate(5deg);
        filter: blur(2px);
    }
    100% {
        opacity: 0.9;
        transform: scale(1) rotate(0deg);
        filter: blur(0px);
    }
}

.add-member-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(30px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05); /* 减少阴影强度 */
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 1px solid rgba(255, 255, 255, 0.4); /* 更柔和的边框 */
}

.add-member-header h3 {
    font-size: 20px;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    flex: 1;
    text-align: center;
    position: relative;
    animation: titleGlow 2s ease-in-out infinite alternate;
    pointer-events: none; /* 防止标题阻止按钮点击 */
}

@keyframes titleGlow {
    0% { filter: drop-shadow(0 0 5px rgba(102, 126, 234, 0.3)); }
    100% { filter: drop-shadow(0 0 15px rgba(102, 126, 234, 0.6)); }
}

.add-member-header h3::before {
    content: '👥✨';
    position: absolute;
    left: -40px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    animation: bounce 2s infinite;
    pointer-events: none; /* 确保伪元素不会阻止点击事件 */
}

.add-member-header h3::after {
    content: '✨👥';
    position: absolute;
    right: -40px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 18px;
    animation: bounce 2s infinite 0.5s;
    pointer-events: none; /* 确保伪元素不会阻止点击事件 */
}

/* 返回按钮美化 */
.back-btn {
    width: 44px;
    height: 44px;
    border: none;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    font-size: 18px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    z-index: 20; /* 确保按钮在最上层 */
}

.back-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s;
    pointer-events: none; /* 确保伪元素不会阻止点击事件 */
}

.back-btn:hover {
    transform: scale(1.1) rotate(-10deg);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.5);
}

.back-btn:hover::before {
    left: 100%;
}

.back-btn:active {
    transform: scale(0.95);
}

/* 确认按钮超级美化 */
.confirm-add-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 50%, #ff9ff3 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.confirm-add-btn::before {
    content: '🎉';
    position: absolute;
    left: -30px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
    transition: all 0.4s ease;
}

.confirm-add-btn:not(:disabled):hover::before {
    left: 8px;
    animation: celebrateIcon 0.6s ease;
}

@keyframes celebrateIcon {
    0%, 100% { transform: translateY(-50%) scale(1); }
    50% { transform: translateY(-50%) scale(1.2) rotate(15deg); }
}

.confirm-add-btn:disabled {
    background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.confirm-add-btn:disabled::before {
    content: ''; /* 移除表情包 */
    left: 8px;
}

.confirm-add-btn:not(:disabled):hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
}

.confirm-add-btn:not(:disabled):active {
    transform: translateY(-1px) scale(1.02);
}

.selected-count {
    background: rgba(255, 255, 255, 0.3);
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.add-member-content {
    flex: 1;
    padding: 25px;
    position: relative;
    overflow-y: auto; /* 只在内容区域允许垂直滚动 */
    max-height: calc(100vh - 80px); /* 减去头部高度，确保不超出视窗 */
}

/* 添加浮动装饰 */
.add-member-content::before {
    content: '✨';
    position: absolute;
    top: 20px;
    right: 30px;
    font-size: 24px;
    animation: twinkle 3s infinite;
    z-index: 1;
}

.add-member-content::after {
    content: '💫';
    position: absolute;
    bottom: 30px;
    left: 30px;
    font-size: 20px;
    animation: twinkle 3s infinite 1.5s;
    z-index: 1;
}

@keyframes twinkle {
    0%, 100% { opacity: 0.3; transform: scale(1) rotate(0deg); }
    50% { opacity: 1; transform: scale(1.2) rotate(180deg); }
}

.member-search-container {
    margin-bottom: 25px;
    position: relative;
}

.member-search-container::before {
    content: '🔍 搜索好友，一起聊天吧~';
    position: absolute;
    top: -30px;
    left: 0;
    font-size: 15px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    animation: searchHint 3s ease-in-out infinite;
}

/* 区域标签样式 - 修复文字显示不完整问题 */
.section-label {
    display: block;
    width: 100%;
    font-size: 16px;
    font-weight: 700;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 15px;
    padding: 8px 0;
    text-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%),
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.08) 0%, transparent 70%);
    border-radius: 12px;
    padding: 12px 18px;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 2;
    overflow: visible;
    white-space: nowrap;
    text-overflow: visible;
    box-sizing: border-box;
    min-width: 0;
    flex-shrink: 0;
}

@keyframes searchHint {
    0%, 100% { opacity: 0.9; transform: translateX(0); }
    50% { opacity: 1; transform: translateX(3px); }
}

.search-input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 25px;
    padding: 4px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
    /* 移除 transform: translateY(-2px); 防止元素移动 */
    box-shadow: 0 12px 40px rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.5);
}

/* 搜索输入框超级美化 */
.search-input {
    width: 100%;
    padding: 16px 50px 16px 50px;
    border: none;
    border-radius: 20px;
    font-size: 16px;
    background: transparent;
    transition: all 0.3s ease;
    color: #333;
    font-weight: 500;
}

.search-input::placeholder {
    color: rgba(102, 126, 234, 0.6);
    font-weight: 400;
}

.search-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.1);
}

/* 搜索图标美化 */
.search-icon {
    position: absolute;
    left: 20px; /* 调整位置确保图标完整显示 */
    top: 50%;
    transform: translateY(-50%);
    color: rgba(102, 126, 234, 0.7);
    font-size: 18px;
    z-index: 2;
    transition: all 0.3s ease;
    pointer-events: none; /* 防止图标阻止输入框点击 */
}

.search-input-wrapper:focus-within .search-icon {
    color: #667eea;
    transform: translateY(-50%) scale(1.1);
    animation: searchPulse 1s ease-in-out;
}

@keyframes searchPulse {
    0%, 100% { transform: translateY(-50%) scale(1.1); }
    50% { transform: translateY(-50%) scale(1.3); }
}

/* 清除按钮美化 */
.clear-search-btn {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 28px;
    height: 28px;
    border: none;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);
    z-index: 2;
}

.clear-search-btn:hover {
    transform: translateY(-50%) scale(1.1) rotate(90deg);
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.5);
}

/* 已选择成员容器样式 - 用于其他页面 */
.selected-members-container {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    min-height: 50px;
    align-items: center;
    padding: 8px 0;
    position: relative;
    z-index: 1;
}

/* 已选择成员区域样式 - 添加成员页面 */
.add-member-page .selected-members-section {
    margin-bottom: 25px;
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%),
        radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 20px;
    padding: 20px;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.4); /* 更柔和的边框 */
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.08), /* 减少阴影强度 */
        0 4px 12px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 1;
}

/* 好友列表区域样式 - 添加成员页面 */
.add-member-page .friends-list-section {
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%),
        radial-gradient(circle at 70% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 20px;
    padding: 20px;
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.4); /* 更柔和的边框 */
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.08), /* 减少阴影强度 */
        0 4px 12px rgba(0, 0, 0, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 1;
}



/* 添加成员页面的好友列表项现代化美化 */
#add-member-friends-list .friend-item {
    display: flex;
    align-items: center;
    padding: 14px 18px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.04); /* 更柔和的边框 */
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.02) 100%),
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.03) 0%, transparent 70%);
    border-radius: 18px;
    margin: 3px 12px;
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 1;
}

#add-member-friends-list .friend-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(135deg, rgba(99, 102, 241, 0.08) 0%, rgba(139, 92, 246, 0.08) 100%),
        radial-gradient(circle at 30% 30%, rgba(236, 72, 153, 0.05) 0%, transparent 70%);
    border-radius: 18px;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: -1;
}

#add-member-friends-list .friend-item:hover::before {
    opacity: 1;
    transform: scale(1.01);
}

#add-member-friends-list .friend-item:hover {
    transform: translateY(-1px);
    box-shadow:
        0 6px 20px rgba(99, 102, 241, 0.12),
        0 3px 10px rgba(139, 92, 246, 0.08),
        0 1px 4px rgba(236, 72, 153, 0.05);
}

#add-member-friends-list .friend-item.selected {
    background:
        linear-gradient(135deg, rgba(99, 102, 241, 0.15) 0%, rgba(139, 92, 246, 0.15) 100%),
        radial-gradient(circle at 30% 30%, rgba(236, 72, 153, 0.08) 0%, transparent 70%);
    border: 2px solid rgba(99, 102, 241, 0.3);
    transform: translateY(-1px);
    box-shadow:
        0 8px 25px rgba(99, 102, 241, 0.2),
        0 4px 12px rgba(139, 92, 246, 0.15),
        0 2px 6px rgba(236, 72, 153, 0.1);
}

#add-member-friends-list .friend-item.selected::after {
    content: '✨';
    position: absolute;
    top: 8px;
    right: 8px;
    font-size: 16px;
    animation: selectedSparkle 1.5s ease-in-out infinite;
}

@keyframes selectedSparkle {
    0%, 100% { opacity: 0.5; transform: scale(1) rotate(0deg); }
    50% { opacity: 1; transform: scale(1.2) rotate(180deg); }
}

#add-member-friends-list .friend-item.selected .selection-indicator {
    opacity: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    animation: checkPulse 0.6s ease-out;
}

@keyframes checkPulse {
    0% { transform: scale(0.8); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* 头像现代化美化 */
#add-member-friends-list .friend-avatar {
    width: 44px;
    height: 44px;
    border-radius: 50%;
    background:
        linear-gradient(135deg, #6366f1 0%, #8b5cf6 50%, #ec4899 100%),
        radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2) 0%, transparent 70%);
    background-size: cover;
    background-position: center;
    margin-right: 14px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 700;
    font-size: 15px;
    position: relative;
    box-shadow:
        0 6px 18px rgba(99, 102, 241, 0.25),
        0 3px 8px rgba(139, 92, 246, 0.15),
        0 1px 4px rgba(236, 72, 153, 0.1);
    transition: all 0.3s ease;
    z-index: 2;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

#add-member-friends-list .friend-avatar::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #667eea);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    animation: rotateGradient 3s linear infinite;
    transition: opacity 0.3s ease;
}

#add-member-friends-list .friend-item:hover .friend-avatar::before {
    opacity: 1;
}

@keyframes rotateGradient {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 好友信息现代化美化 */
#add-member-friends-list .friend-info {
    flex: 1;
    min-width: 0;
    z-index: 2;
    position: relative;
}

#add-member-friends-list .friend-name {
    font-size: 16px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.95);
    margin-bottom: 4px;
    transition: all 0.3s ease;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

#add-member-friends-list .friend-item:hover .friend-name {
    transform: translateX(2px);
    color: rgba(255, 255, 255, 1);
}

#add-member-friends-list .friend-status {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    position: relative;
}

#add-member-friends-list .friend-status::before {
    content: '🌟 ';
    font-size: 12px;
    margin-right: 2px;
}

/* 选择指示器现代化美化 */
#add-member-friends-list .selection-indicator {
    width: 26px;
    height: 26px;
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0.8;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    font-size: 13px;
    background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%),
        radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 70%);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    position: relative;
    z-index: 3;
    color: rgba(255, 255, 255, 0.9);
}

#add-member-friends-list .selection-indicator::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #667eea);
    z-index: -1;
    opacity: 0;
    animation: rotateGradient 2s linear infinite;
    transition: opacity 0.3s ease;
}

#add-member-friends-list .friend-item:hover .selection-indicator::before {
    opacity: 0.3;
}

#add-member-friends-list .friend-item.selected .selection-indicator::before {
    opacity: 1;
}

/* 空状态消息美化 - 更友好的设计 */
.no-friends-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 80px 30px;
    text-align: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
    border-radius: 25px;
    margin: 20px;
    backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

/* 添加装饰性背景 */
.no-friends-message::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
    z-index: 0;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-friends-message .empty-icon {
    font-size: 80px;
    margin-bottom: 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: float 3s ease-in-out infinite;
    position: relative;
    z-index: 1;
}

@keyframes float {
    0%, 100% { transform: translateY(0) scale(1); }
    50% { transform: translateY(-15px) scale(1.05); }
}

.no-friends-message .empty-title {
    font-size: 20px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 700;
    margin: 0 0 15px 0;
    position: relative;
    z-index: 1;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.no-friends-message .empty-description {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.7);
    font-weight: 500;
    margin: 0 0 25px 0;
    line-height: 1.6;
    position: relative;
    z-index: 1;
}

.no-friends-message .empty-suggestions {
    display: flex;
    flex-direction: column;
    gap: 12px;
    position: relative;
    z-index: 1;
}

.no-friends-message .suggestion-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.no-friends-message .suggestion-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

.no-friends-message .suggestion-icon {
    font-size: 16px;
    opacity: 0.8;
}

/* 已选择成员标签美化 */
.selected-add-member {
    display: inline-flex;
    align-items: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin: 4px;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
    animation: memberAppear 0.5s ease-out;
}

@keyframes memberAppear {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(10px);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.selected-add-member::before {
    content: '👤';
    margin-right: 6px;
    font-size: 12px;
}

.selected-add-member:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.selected-add-member .remove-member-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin-left: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: all 0.3s ease;
}

.selected-add-member .remove-member-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1) rotate(90deg);
}

/* 添加一些可爱的加载动画 */
.loading-friends {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px;
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
}

.loading-friends::before {
    content: '🔄';
    margin-right: 10px;
    font-size: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 480px) {
    .add-member-header {
        padding: 15px;
    }

    .add-member-header h3 {
        font-size: 18px;
    }

    .add-member-content {
        padding: 20px 15px;
    }

    #add-member-friends-list .friend-item {
        padding: 12px 15px;
        margin: 2px 4px;
    }

    #add-member-friends-list .friend-avatar {
        width: 42px;
        height: 42px;
    }
}
}

/* 移动端适配 */
@media (max-width: 480px) {
    .add-member-header {
        padding: 15px;
    }

    .add-member-content {
        padding: 15px;
    }

    .search-input {
        padding: 10px 14px 10px 36px;
        font-size: 15px;
    }

    .search-icon {
        left: 10px;
    }

    .selected-members-section,
    .friends-list-section {
        margin-bottom: 15px;
    }
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    .create-group-chat,
    .group-chat-detail {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    }

    .group-info-section,
    .member-selection-section,
    .group-info-card,
    .group-members-section,
    .group-actions-section {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
    }

    .group-chat-header,
    .group-detail-header {
        background: rgba(0, 0, 0, 0.8);
    }

    .friend-item:hover {
        background: rgba(255, 255, 255, 0.1);
    }

    .friend-item.selected {
        background: rgba(7, 193, 96, 0.2);
    }

    .action-item:hover {
        background: rgba(255, 255, 255, 0.05);
    }

    .action-item.danger-action:hover {
        background: rgba(239, 68, 68, 0.1);
    }
}

/* 无障碍优化 */
.friend-item:focus,
.member-item:focus,
.action-item:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.group-chat-btn:focus {
    outline: 2px solid white;
    outline-offset: 2px;
}

.confirm-btn:focus,
.add-member-btn:focus {
    outline: 2px solid rgba(255, 255, 255, 0.5);
    outline-offset: 2px;
}

/* 隐藏滚动条但保持功能 */
.friends-list-container::-webkit-scrollbar,
.group-members-grid::-webkit-scrollbar {
    width: 6px;
}

.friends-list-container::-webkit-scrollbar-track,
.group-members-grid::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

.friends-list-container::-webkit-scrollbar-thumb,
.group-members-grid::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 3px;
}

.friends-list-container::-webkit-scrollbar-thumb:hover,
.group-members-grid::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
}

/* 加载状态动画 */
@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* 触摸反馈优化 */
@media (hover: none) and (pointer: coarse) {
    .friend-item:active {
        background: rgba(7, 193, 96, 0.15);
        transform: scale(0.98);
    }

    .member-item:active {
        transform: scale(0.95);
    }

    .action-item:active {
        background: rgba(0, 0, 0, 0.1);
    }

    .group-chat-btn:active {
        transform: translateY(-50%) scale(0.9);
    }
}

/* 打印样式优化 */
@media print {
    .create-group-chat,
    .group-chat-detail {
        position: static;
        transform: none;
        background: white;
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .group-chat-header,
    .group-detail-header {
        background: white;
        border-bottom: 1px solid #ccc;
    }

    .group-chat-btn {
        display: none;
    }
}

/* 长列表性能优化 */
.friends-list-container {
    contain: layout style paint;
}

.group-members-grid {
    contain: layout style paint;
}

/* 内容安全边距 */
.create-group-chat,
.group-chat-detail {
    padding-bottom: env(safe-area-inset-bottom);
}

@supports (padding: max(0px)) {
    .group-chat-header,
    .group-detail-header {
        padding-left: max(20px, env(safe-area-inset-left));
        padding-right: max(20px, env(safe-area-inset-right));
        padding-top: max(20px, env(safe-area-inset-top));
    }
}

/* 大厂卖萌风格装饰效果 */
.create-group-chat::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.08)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.06)"/><circle cx="70" cy="70" r="1.2" fill="rgba(255,255,255,0.07)"/><circle cx="10" cy="60" r="0.8" fill="rgba(255,255,255,0.05)"/></svg>') repeat,
        radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 90% 80%, rgba(255, 255, 255, 0.03) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
    animation: twinkle 8s ease-in-out infinite;
}

@keyframes twinkle {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 0.8; }
}

/* 可爱的加载动画 */
.cute-loading {
    display: inline-block;
    position: relative;
    width: 40px;
    height: 40px;
}

.cute-loading::before {
    content: '🌟';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    animation: cuteRotate 2s linear infinite;
}

@keyframes cuteRotate {
    0% { transform: translate(-50%, -50%) rotate(0deg) scale(1); }
    25% { transform: translate(-50%, -50%) rotate(90deg) scale(1.2); }
    50% { transform: translate(-50%, -50%) rotate(180deg) scale(1); }
    75% { transform: translate(-50%, -50%) rotate(270deg) scale(1.2); }
    100% { transform: translate(-50%, -50%) rotate(360deg) scale(1); }
}

/* 成功状态的可爱效果 */
.success-celebration {
    position: relative;
}

.success-celebration::before {
    content: '🎉';
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 16px;
    animation: celebrate 1s ease-out;
}

@keyframes celebrate {
    0% {
        transform: scale(0) rotate(0deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.5) rotate(180deg);
        opacity: 1;
    }
    100% {
        transform: scale(1) rotate(360deg);
        opacity: 0;
    }
}

/* 悬浮提示气泡 */
.cute-tooltip {
    position: relative;
    display: inline-block;
}

.cute-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.cute-tooltip::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%) translateY(100%);
    border: 6px solid transparent;
    border-top-color: #667eea;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cute-tooltip:hover::after,
.cute-tooltip:hover::before {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-8px);
}

/* 心跳动画效果 */
.heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    14% { transform: scale(1.1); }
    28% { transform: scale(1); }
    42% { transform: scale(1.1); }
    70% { transform: scale(1); }
}

/* 彩虹边框效果 */
.rainbow-border {
    position: relative;
    background: white;
    border-radius: 20px;
    overflow: hidden;
}

.rainbow-border::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3);
    background-size: 400% 400%;
    border-radius: 22px;
    z-index: -1;
    animation: rainbowShift 3s ease infinite;
}

@keyframes rainbowShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 可爱的错误提示 */
.cute-error {
    position: relative;
    color: #f56565;
    font-weight: 500;
}

.cute-error::before {
    content: '😅';
    margin-right: 8px;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* 新增HTML元素的样式 */
.btn-loading {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.input-tips {
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(245, 87, 108, 0.05) 100%);
    border-radius: 20px;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.tip-icon {
    font-size: 16px;
    animation: pulse 2s ease-in-out infinite;
}

.tip-text {
    font-size: 13px;
    color: #667eea;
    font-weight: 500;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.empty-selection-hint {
    display: flex;
    flex-direction: row; /* 改为水平布局 */
    align-items: center;
    justify-content: center;
    padding: 4px 24px; /* 大幅减少padding */
    color: #a0aec0;
    font-size: 13px; /* 稍微减少字体大小 */
    font-weight: 500;
    text-align: center;
    width: 100%; /* 确保占满容器宽度 */
    box-sizing: border-box; /* 包含padding在宽度计算中 */
    gap: 8px; /* 添加emoji和文字之间的间距 */
}

.hint-emoji {
    font-size: 18px; /* 减少emoji大小 */
    margin-bottom: 0; /* 移除下边距 */
    animation: wave 2s ease-in-out infinite;
}

@keyframes wave {
    0%, 100% { transform: rotate(0deg); }
    25% { transform: rotate(20deg); }
    75% { transform: rotate(-20deg); }
}

.hint-text {
    opacity: 0.8;
}

/* 加载占位符样式 */
.loading-placeholder {
    padding: 20px 24px;
}

.loading-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    gap: 16px;
}

.loading-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

.loading-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.loading-name {
    height: 16px;
    width: 120px;
    border-radius: 8px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

.loading-status {
    height: 12px;
    width: 80px;
    border-radius: 6px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

/* 浮动装饰元素 */
.floating-decorations {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 1;
    overflow: hidden;
}

.decoration-item {
    position: absolute;
    font-size: 20px;
    opacity: 0.6;
    animation: float-decoration 8s ease-in-out infinite;
}

.decoration-1 {
    top: 15%;
    left: 10%;
    animation-delay: 0s;
}

.decoration-2 {
    top: 25%;
    right: 15%;
    animation-delay: 2s;
}

.decoration-3 {
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
}

.decoration-4 {
    bottom: 20%;
    right: 10%;
    animation-delay: 6s;
}

@keyframes float-decoration {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }
    25% {
        transform: translateY(-20px) rotate(90deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-10px) rotate(180deg);
        opacity: 0.8;
    }
    75% {
        transform: translateY(-30px) rotate(270deg);
        opacity: 0.4;
    }
}

/* 头像光晕效果 */
.avatar-glow {
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    z-index: -1;
    opacity: 0.3;
    animation: glow-pulse 3s ease-in-out infinite;
}

@keyframes glow-pulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.6;
    }
}

/* 按钮加载状态 */
.confirm-btn.loading {
    pointer-events: none;
    opacity: 0.8;
}

.confirm-btn.loading::before {
    animation: none;
}

/* 输入框震动效果 */
.shake {
    animation: inputShake 0.5s ease-in-out;
}

@keyframes inputShake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* 响应式优化 */
@media (max-width: 480px) {
    .floating-decorations {
        display: none; /* 在小屏幕上隐藏装饰元素以提高性能 */
    }

    .group-avatar-preview {
        width: 100px;
        height: 100px;
        font-size: 40px;
    }

    .change-avatar-btn {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .input-tips {
        padding: 10px 16px;
        margin-top: 12px;
    }

    .tip-text {
        font-size: 12px;
    }
}


