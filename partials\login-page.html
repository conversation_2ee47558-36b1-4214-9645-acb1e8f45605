<div class="login-page">
  <!-- 微信启动页背景 -->
  <div class="wechat-splash-bg">
    <!-- 地球 -->
    <div class="earth-sphere">
      <div class="earth-surface"></div>
      <div class="earth-clouds"></div>
      <div class="earth-glow"></div>
    </div>

    <!-- 登录表单 -->
    <div class="login-container">
    <div class="wechat-branding">
      <div class="wechat-icon">
        <i class="fab fa-weixin"></i>
      </div>
      <h1>微信</h1>
    </div>

    <div class="login-form" id="login-form">
      <div class="form-group">
        <input type="text" id="login-username" placeholder="用户名" required>
      </div>
      <div class="form-group">
        <input type="password" id="login-password" placeholder="密码" required>
      </div>
      <button id="login-btn" class="login-btn">登录</button>

      <div class="form-switch">没有账号？<span id="show-register">立即注册</span></div>
    </div>

    <div class="login-form" id="register-form" style="display:none;">
      <div class="form-group">
        <input type="text" id="register-username" placeholder="用户名" required>
      </div>
      <div class="form-group">
        <input type="password" id="register-password" placeholder="密码" required>
      </div>
      <button id="register-btn" class="register-btn">注册</button>
      <div class="form-switch">
        已有账号？<span id="show-login">立即登录</span>
      </div>
    </div>

    </div>
  </div>
</div>

<style>
/* 微信启动页风格登录界面 */
.login-page {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  overflow: hidden;
}

/* 微信启动页背景 */
.wechat-splash-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(180deg,
    #000814 0%,
    #001d3d 20%,
    #003566 40%,
    #0077b6 60%,
    #00b4d8 80%,
    #90e0ef 100%
  );
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  padding: 0 5%;
}

/* 地球球体 */
.earth-sphere {
  position: relative;
  width: 350px;
  height: 350px;
  border-radius: 50%;
  /* 移除自身背景，避免与贴图叠出第二层边缘 */
  background: none;
  /* 移除全部阴影，避免形成第二层边缘 */
  box-shadow: none !important;
  animation: earthFloat 6s ease-in-out infinite;
  overflow: hidden;
  flex-shrink: 0;
  z-index: 15;
  cursor: pointer;
  transition: transform 0.8s ease, filter 0.8s ease, box-shadow 0.8s ease;
}

/* 边缘柔化遮罩，去除贴图边缘产生的第二层感 */
/* 悬停极光扫光层 */
.earth-sphere::after { z-index: 2;
  content: '';
  position: absolute;
  inset: -1px;
  border-radius: 50%;
  background: conic-gradient(from 0deg,
    rgba(80, 200, 255, 0.0) 0deg,
    rgba(80, 200, 255, 0.25) 30deg,
    rgba(150, 255, 200, 0.25) 60deg,
    rgba(255, 255, 255, 0.0) 120deg,
    rgba(255, 255, 255, 0.0) 360deg);
  mix-blend-mode: screen;
  filter: blur(6px);
  opacity: 0;
  pointer-events: none;
}

/* 悬停：轻微放大倾斜 + 扫光动画 + 云层加速 */
.earth-sphere:hover,
.earth-sphere.is-hover {
  transform: translateY(-10px) scale(1.04) rotateX(6deg) rotateY(-4deg);
  box-shadow: none !important;
}

.earth-sphere:hover::after,
.earth-sphere.is-hover::after {
  opacity: 0.45;
  animation: auroraSweep 3.5s linear infinite;
}

.earth-sphere:hover .earth-clouds,
.earth-sphere.is-hover .earth-clouds {
  animation-duration: 16s;
  opacity: 0.9;
}

@keyframes auroraSweep {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.earth-surface {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  /* 使用真实地球贴图 */
  background: url('images/earth.png') center / cover no-repeat;
  /* 边缘柔化遮罩，裁掉贴图外延高光 */
  -webkit-mask-image: radial-gradient(circle, rgba(0,0,0,1) 63%, rgba(0,0,0,0) 65%);
  mask-image: radial-gradient(circle, rgba(0,0,0,1) 63%, rgba(0,0,0,0) 65%);
  animation: earthRotate 30s linear infinite;
}

.earth-clouds {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background:
    /* 云层效果 */
    radial-gradient(ellipse 120px 60px at 20% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 70%),
    radial-gradient(ellipse 80px 40px at 60% 20%, rgba(255, 255, 255, 0.3) 0%, transparent 65%),
    radial-gradient(ellipse 100px 50px at 75% 60%, rgba(255, 255, 255, 0.35) 0%, transparent 70%),
    radial-gradient(ellipse 90px 45px at 40% 80%, rgba(255, 255, 255, 0.3) 0%, transparent 65%);
  -webkit-mask-image: radial-gradient(circle, rgba(0,0,0,1) 63%, rgba(0,0,0,0) 65%);
  mask-image: radial-gradient(circle, rgba(0,0,0,1) 63%, rgba(0,0,0,0) 65%);
  animation: cloudsMove 40s linear infinite;
}

/* 去除外圈光晕 */
.earth-glow { display: none; }



/* 登录容器 */
.login-container {
  position: relative;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  width: 320px;
  max-width: 400px;
  padding: 32px 24px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.2),
    0 8px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-align: center;
  flex-shrink: 0;
}

/* 微信品牌 */
.wechat-branding {
  text-align: center;
  margin-bottom: 32px;
}

.wechat-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  background: linear-gradient(135deg, #07c160 0%, #05a050 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 20px rgba(7, 193, 96, 0.3);
}

.wechat-icon i {
  font-size: 32px;
  color: white;
}

.wechat-branding h1 {
  font-size: 24px;
  font-weight: 300;
  color: #333;
  margin: 0;
  letter-spacing: 1px;
}

/* 表单样式 */
.form-group {
  margin-bottom: 16px;
  text-align: left;
}

.form-group input {
  width: 100%;
  padding: 14px 16px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #07c160;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 0 0 2px rgba(7, 193, 96, 0.1);
}

.login-btn, .register-btn {
  width: 100%;
  padding: 14px;
  background: linear-gradient(135deg, #07c160 0%, #05a050 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  margin-bottom: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(7, 193, 96, 0.3);
}

.login-btn:hover, .register-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(7, 193, 96, 0.4);
}

.login-btn:active, .register-btn:active {
  transform: translateY(0);
}

.login-btn:disabled, .register-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.form-switch {
  text-align: center;
  font-size: 14px;
  color: #666;
}

.form-switch span {
  color: #07c160;
  cursor: pointer;
  font-weight: 500;
  margin-left: 4px;
  transition: color 0.3s ease;
}

.form-switch span:hover {
  color: #05a050;
}



/* 动画效果 */
@keyframes earthFloat {
  0%, 100% {
    transform: translateY(0px) scale(1);
  }
  50% {
    transform: translateY(-10px) scale(1.02);
  }
}

@keyframes earthRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes cloudsMove {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}



/* 响应式设计 */
@media (max-width: 1024px) {
  .wechat-splash-bg {
    flex-direction: column;
    justify-content: center;
    padding: 40px 5%;
  }

  .earth-sphere {
    width: 300px;
    height: 300px;
    margin-bottom: 40px;
  }

  .login-container {
    width: 90%;
    max-width: 400px;
    padding: 24px 20px;
  }
}

@media (max-width: 768px) {
  .earth-sphere {
    width: 250px;
    height: 250px;
    margin-bottom: 30px;
  }

  .login-container {
    width: 90%;
    padding: 24px 20px;
  }

  .wechat-icon {
    width: 56px;
    height: 56px;
  }

  .wechat-icon i {
    font-size: 28px;
  }

  .wechat-branding h1 {
    font-size: 22px;
  }
}

@media (max-width: 480px) {
  .earth-sphere {
    width: 200px;
    height: 200px;
    margin-bottom: 20px;
  }

  .login-container {
    width: 95%;
    padding: 20px 16px;
  }

  .wechat-icon {
    width: 48px;
    height: 48px;
  }

  .wechat-icon i {
    font-size: 24px;
  }

  .wechat-branding h1 {
    font-size: 20px;
  }
}
/* 强制去除任何外圈阴影/投影（保险兜底） */
.earth-sphere, .earth-sphere * { filter: none !important; text-shadow: none !important; }
</style>