/* 相册页面样式 - 大厂高端设计 */

/* 页面头部 - 大厂级现代化设计 */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 36px;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.98) 0%,
        rgba(248, 250, 252, 0.95) 50%,
        rgba(241, 245, 249, 0.98) 100%);
    backdrop-filter: blur(40px) saturate(200%);
    border-bottom: 1px solid rgba(34, 197, 94, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.06),
        0 1px 0 rgba(255, 255, 255, 0.8) inset;
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg,
        rgba(7, 193, 96, 0.02) 0%,
        rgba(6, 174, 86, 0.02) 50%,
        rgba(32, 208, 119, 0.02) 100%);
    z-index: -1;
}

.page-header h2 {
    font-size: 32px;
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    letter-spacing: -1px;
    position: relative;
    filter: drop-shadow(0 2px 4px rgba(7, 193, 96, 0.1));
}

.back-btn, .create-album-btn {
    width: 52px;
    height: 52px;
    border: none;
    border-radius: 18px;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-primary);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.back-btn::before, .create-album-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.back-btn::after, .create-album-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transition: all 0.4s ease;
}

.back-btn:hover::before, .create-album-btn:hover::before {
    left: 100%;
}

.back-btn:hover::after, .create-album-btn:hover::after {
    width: 100px;
    height: 100px;
}

.back-btn:hover, .create-album-btn:hover {
    transform: translateY(-3px) scale(1.08);
    box-shadow: var(--shadow-primary-lg);
}

.back-btn:active, .create-album-btn:active {
    transform: translateY(-1px) scale(1.05);
}

.back-btn i, .create-album-btn i {
    font-size: 18px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.header-actions {
    display: flex;
    gap: 12px;
}

.edit-album-btn, .delete-album-btn {
    width: 44px;
    height: 44px;
    border: none;
    border-radius: 14px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    color: #64748b;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.delete-album-btn {
    color: #ef4444;
}

.edit-album-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(100, 116, 139, 0.2);
    background: rgba(100, 116, 139, 0.1);
}

.delete-album-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.2);
    background: rgba(239, 68, 68, 0.1);
}

/* 相册内容 - 现代化布局 */
.albums-content {
    padding: 32px;
    min-height: calc(100vh - 100px);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    position: relative;
    overflow: hidden;
}

.albums-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 80%, rgba(7, 193, 96, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(6, 174, 86, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(32, 208, 119, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: 0;
}

/* 相册页面头部统计 */
.albums-header {
    margin-bottom: 40px;
}

.albums-stats {
    display: flex;
    gap: 32px;
    justify-content: center;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(24px) saturate(180%);
    border-radius: 24px;
    padding: 32px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.4);
    position: relative;
    overflow: hidden;
}

.albums-stats::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.02) 0%, rgba(22, 163, 74, 0.02) 100%);
    z-index: -1;
}

.stat-item {
    text-align: center;
    position: relative;
}

.stat-item:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -16px;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 40px;
    background: linear-gradient(to bottom, transparent, rgba(100, 116, 139, 0.3), transparent);
}

.stat-number {
    display: block;
    font-size: 32px;
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: 1;
    margin-bottom: 8px;
    letter-spacing: -1px;
}

.stat-label {
    font-size: 14px;
    color: #64748b;
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* 相册网格 - 大厂级高端卡片设计 */
.albums-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(340px, 1fr));
    gap: 32px;
    margin-bottom: 48px;
    position: relative;
    z-index: 1;
}

.album-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(40px) saturate(180%);
    border-radius: 28px;
    overflow: hidden;
    box-shadow:
        0 16px 48px rgba(0, 0, 0, 0.08),
        0 8px 24px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    border: 1px solid rgba(255, 255, 255, 0.6);
    position: relative;
    transform-style: preserve-3d;
    transform-origin: center center;
}

.album-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 0.08) 0%,
        rgba(22, 163, 74, 0.08) 50%,
        rgba(21, 128, 61, 0.08) 100%);
    opacity: 0;
    transition: all 0.4s ease;
    border-radius: 28px;
    z-index: 1;
}

.album-card::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
        rgba(34, 197, 94, 0.3) 0%,
        rgba(22, 163, 74, 0.3) 50%,
        rgba(21, 128, 61, 0.3) 100%);
    border-radius: 30px;
    opacity: 0;
    transition: all 0.4s ease;
    z-index: -1;
    filter: blur(8px);
}

.album-card:hover::before {
    opacity: 1;
}

.album-card:hover::after {
    opacity: 1;
}

.album-card:hover {
    transform: translateY(-12px) rotateX(3deg) rotateY(2deg);
    box-shadow:
        0 32px 80px rgba(0, 0, 0, 0.15),
        0 16px 40px rgba(34, 197, 94, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.album-cover {
    width: 100%;
    height: 260px;
    background: var(--primary-gradient-vibrant);
    background-size: cover;
    background-position: top center;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.album-cover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        linear-gradient(45deg, rgba(0, 0, 0, 0.1) 0%, transparent 30%, rgba(255, 255, 255, 0.15) 70%, transparent 100%),
        radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(0, 0, 0, 0.05) 0%, transparent 50%);
    transition: all 0.4s ease;
}

.album-card:hover .album-cover::before {
    background:
        linear-gradient(45deg, rgba(0, 0, 0, 0.05) 0%, transparent 30%, rgba(255, 255, 255, 0.2) 70%, transparent 100%),
        radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(0, 0, 0, 0.03) 0%, transparent 50%);
}

.album-cover i {
    font-size: 72px;
    color: rgba(255, 255, 255, 0.9);
    z-index: 2;
    filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.3));
    animation: breathe 3s ease-in-out infinite;
}

@keyframes breathe {
    0%, 100% { transform: scale(1); opacity: 0.9; }
    50% { transform: scale(1.05); opacity: 1; }
}

.album-info {
    padding: 28px;
    position: relative;
    z-index: 2;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
    backdrop-filter: blur(20px);
}

.album-name {
    font-size: 22px;
    font-weight: 800;
    background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 16px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    letter-spacing: -0.5px;
    line-height: 1.2;
    transition: all 0.3s ease;
}

.album-card:hover .album-name {
    transform: translateY(-2px);
}

.album-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
    margin-bottom: 12px;
    gap: 12px;
}

.album-stats span {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(22, 163, 74, 0.1) 100%);
    padding: 6px 16px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 700;
    letter-spacing: 0.5px;
    color: #475569;
    border: 1px solid rgba(34, 197, 94, 0.1);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.album-card:hover .album-stats span {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.15) 0%, rgba(22, 163, 74, 0.15) 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.1);
}

.album-description {
    font-size: 15px;
    color: #64748b;
    margin-top: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.5;
    font-weight: 500;
    transition: all 0.3s ease;
}

.album-card:hover .album-description {
    color: #475569;
}

/* 空状态 - 现代简约设计 */
.empty-state, .empty-media-state {
    text-align: center;
    padding: 80px 40px 60px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
    backdrop-filter: blur(20px) saturate(120%);
    border-radius: 32px;
    border: 1px solid rgba(255, 255, 255, 0.8);
    margin: 40px auto;
    max-width: 520px;
    position: relative;
    overflow: hidden;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.06),
        0 8px 24px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
    z-index: 1;
    transition: all 0.3s ease;
}

.empty-state:hover, .empty-media-state:hover {
    transform: translateY(-4px);
    box-shadow:
        0 32px 80px rgba(0, 0, 0, 0.08),
        0 12px 32px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.empty-state::before, .empty-media-state::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 30%, rgba(34, 197, 94, 0.08) 0%, transparent 60%),
        radial-gradient(circle at 80% 70%, rgba(21, 128, 61, 0.06) 0%, transparent 60%),
        linear-gradient(135deg, rgba(34, 197, 94, 0.03) 0%, transparent 50%);
    z-index: -1;
}

.empty-icon {
    position: relative;
    margin-bottom: 32px;
    display: inline-block;
}

.empty-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 160px;
    height: 160px;
    background: radial-gradient(circle, rgba(34, 197, 94, 0.08) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulse 4s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: translate(-50%, -50%) scale(1); opacity: 0.5; }
    50% { transform: translate(-50%, -50%) scale(1.15); opacity: 0.8; }
}

.empty-state i, .empty-media-state i {
    font-size: 96px;
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 70%, #15803d 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 4px 12px rgba(34, 197, 94, 0.25));
    animation: float 6s ease-in-out infinite;
    position: relative;
    z-index: 2;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

.empty-state h3, .empty-media-state h3 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 16px;
    color: #1e293b;
    letter-spacing: -0.5px;
    line-height: 1.3;
}

.empty-state p, .empty-media-state p {
    font-size: 16px;
    margin-bottom: 40px;
    color: #64748b;
    line-height: 1.6;
    max-width: 360px;
    margin-left: auto;
    margin-right: auto;
    font-weight: 400;
}

.create-first-album-btn {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: 24px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 12px;
    box-shadow:
        0 8px 24px rgba(34, 197, 94, 0.25),
        0 4px 12px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    letter-spacing: 0.3px;
    backdrop-filter: blur(10px);
    min-width: 160px;
    justify-content: center;
}

.create-first-album-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.create-first-album-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transition: all 0.4s ease;
}

.create-first-album-btn:hover::before {
    left: 100%;
}

.create-first-album-btn:hover::after {
    width: 300px;
    height: 300px;
}

.create-first-album-btn:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow:
        0 12px 32px rgba(34, 197, 94, 0.35),
        0 6px 16px rgba(0, 0, 0, 0.15);
}

.create-first-album-btn:active {
    transform: translateY(-2px) scale(1.02);
}

.create-first-album-btn i {
    font-size: 20px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

/* 弹窗样式 - 现代化设计 */
.album-modal, .confirm-modal, .media-preview-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px) saturate(180%);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.album-modal.active, .confirm-modal.active, .media-preview-modal.active {
    display: flex;
    opacity: 1;
}

.album-modal-container, .confirm-modal-container {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(24px) saturate(180%);
    border-radius: 32px;
    width: 90%;
    max-width: 520px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 80px rgba(0, 0, 0, 0.2);
    transform: translateY(40px) scale(0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.4);
    position: relative;
    overflow: hidden;
}

.album-modal-container::before, .confirm-modal-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.02) 0%, rgba(22, 163, 74, 0.02) 100%);
    z-index: -1;
}

.album-modal.active .album-modal-container,
.confirm-modal.active .confirm-modal-container {
    transform: translateY(0) scale(1);
}

.album-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 28px 32px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%);
}

.album-modal-header h3 {
    font-size: 24px;
    font-weight: 700;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin: 0;
    letter-spacing: -0.5px;
}

.close-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 14px;
    background: rgba(100, 116, 139, 0.1);
    color: #64748b;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.close-btn:hover {
    background: rgba(100, 116, 139, 0.2);
    transform: scale(1.05);
}

.album-modal-content {
    padding: 32px;
}

/* 表单样式 - 现代化设计 */
.form-group {
    margin-bottom: 28px;
}

.form-group label {
    display: block;
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 12px;
    letter-spacing: -0.2px;
}

.form-group input, .form-group textarea {
    width: 100%;
    padding: 16px 20px;
    border: 2px solid rgba(100, 116, 139, 0.2);
    border-radius: 16px;
    font-size: 16px;
    background: rgba(255, 255, 255, 0.9);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-sizing: border-box;
    backdrop-filter: blur(10px);
    font-family: inherit;
}

.form-group input:focus, .form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    background: white;
    box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.1);
    transform: translateY(-1px);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
    line-height: 1.6;
}

/* 封面上传区域 */
.cover-upload-area {
    border: 2px dashed rgba(0, 0, 0, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.cover-upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(var(--primary-color-rgb), 0.05);
}

.cover-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
}

.cover-preview i {
    font-size: 32px;
}

.cover-preview img {
    width: 100%;
    height: 200px;
    border-radius: var(--border-radius-md);
    object-fit: cover;
    object-position: top center;
}

/* 封面位置调整控件 */
.cover-position-control {
    margin-top: 16px;
    padding: 16px;
    background: rgba(248, 250, 252, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.06);
}

.cover-position-control label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 12px;
}

.position-options {
    display: flex;
    gap: 8px;
}

.position-btn {
    flex: 1;
    padding: 8px 16px;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    background: white;
    color: #64748b;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.position-btn:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background: rgba(34, 197, 94, 0.05);
}

.position-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    box-shadow: 0 2px 8px rgba(34, 197, 94, 0.3);
}

/* 表单操作按钮 - 高端设计 */
.form-actions {
    display: flex;
    gap: 16px;
    justify-content: flex-end;
    margin-top: 32px;
}

.cancel-btn, .save-btn, .delete-btn {
    padding: 16px 32px;
    border: none;
    border-radius: 16px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 12px;
    position: relative;
    overflow: hidden;
    min-width: 120px;
    justify-content: center;
}

.cancel-btn {
    background: rgba(100, 116, 139, 0.1);
    color: #64748b;
    border: 1px solid rgba(100, 116, 139, 0.2);
}

.save-btn {
    background: var(--primary-gradient);
    color: white;
    box-shadow: var(--shadow-primary);
}

.delete-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 8px 32px rgba(239, 68, 68, 0.3);
}

.cancel-btn::before, .save-btn::before, .delete-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.cancel-btn:hover::before, .save-btn:hover::before, .delete-btn:hover::before {
    left: 100%;
}

.cancel-btn:hover {
    transform: translateY(-2px);
    background: rgba(100, 116, 139, 0.2);
    box-shadow: 0 6px 20px rgba(100, 116, 139, 0.2);
}

.save-btn:hover, .delete-btn:hover {
    transform: translateY(-3px) scale(1.02);
}

.save-btn:hover {
    box-shadow: 0 12px 40px rgba(34, 197, 94, 0.4);
}

.delete-btn:hover {
    box-shadow: 0 12px 40px rgba(239, 68, 68, 0.4);
}

.btn-loading {
    display: none;
}

.save-btn.loading .btn-text {
    display: none;
}

.save-btn.loading .btn-loading {
    display: inline-flex;
}

/* 相册详情页面样式 - 高端设计 */
.album-detail-content {
    padding: 32px;
    min-height: calc(100vh - 100px);
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* 相册详情页面的信息卡片样式 */
.album-detail-content .album-info {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(24px) saturate(180%);
    border-radius: 24px;
    padding: 32px;
    margin-bottom: 32px;
    border: 1px solid rgba(255, 255, 255, 0.4);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    position: relative;
    overflow: hidden;
}

.album-detail-content .album-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.02) 0%, rgba(22, 163, 74, 0.02) 100%);
    z-index: -1;
}

.album-header {
    margin-bottom: 20px;
}

.album-name {
    font-size: 28px;
    font-weight: 800;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 16px;
    letter-spacing: -0.8px;
    line-height: 1.2;
}

.album-stats {
    display: flex;
    gap: 24px;
    margin-bottom: 0;
    font-size: 14px;
    color: #64748b;
    font-weight: 500;
}

.album-stats span {
    background: linear-gradient(135deg, rgba(7, 193, 96, 0.1) 0%, rgba(6, 174, 86, 0.1) 100%);
    padding: 8px 16px;
    border-radius: 16px;
    font-weight: 600;
    letter-spacing: 0.5px;
    border: 1px solid rgba(7, 193, 96, 0.1);
    transition: all 0.3s ease;
}

.album-stats span:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(7, 193, 96, 0.2);
}

.album-description-text {
    font-size: 16px;
    color: #1e293b;
    line-height: 1.6;
    margin: 0;
    font-weight: 400;
}

/* 上传区域 - 现代化设计 */
.upload-section {
    margin-bottom: 40px;
}

.upload-area {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(24px) saturate(180%);
    border: 2px dashed rgba(7, 193, 96, 0.3);
    border-radius: 24px;
    padding: 48px 32px;
    text-align: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.04);
}

.upload-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.02) 0%, rgba(22, 163, 74, 0.02) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.upload-area:hover::before {
    opacity: 1;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(34, 197, 94, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(34, 197, 94, 0.15);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(34, 197, 94, 0.1);
    transform: scale(1.02);
    box-shadow: 0 12px 40px rgba(34, 197, 94, 0.2);
}

.upload-content i {
    font-size: 64px;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 20px;
    filter: drop-shadow(0 4px 8px rgba(34, 197, 94, 0.2));
}

.upload-content h3 {
    font-size: 24px;
    font-weight: 700;
    color: #1e293b;
    margin-bottom: 12px;
    letter-spacing: -0.5px;
}

.upload-content p {
    font-size: 16px;
    color: #64748b;
    margin-bottom: 28px;
    line-height: 1.5;
}

.upload-btn {
    background: var(--primary-gradient);
    color: white;
    border: none;
    padding: 16px 32px;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-primary);
    position: relative;
    overflow: hidden;
}

.upload-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.upload-btn:hover::before {
    left: 100%;
}

.upload-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-primary-lg);
}

/* 上传进度 */
.upload-progress {
    padding: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
}

.progress-fill {
    height: 100%;
    background: var(--primary-gradient);
    width: 0%;
    transition: width var(--transition-fast);
}

.progress-text {
    font-size: 14px;
    color: var(--text-secondary);
    text-align: center;
    display: block;
}

/* 媒体网格 - 高端瀑布流设计 */
.media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 20px;
    margin-top: 32px;
}

.media-item {
    position: relative;
    aspect-ratio: 1;
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(24px) saturate(180%);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.4);
    transform-style: preserve-3d;
}

.media-content {
    position: relative;
    width: 100%;
    height: 100%;
    cursor: pointer;
    overflow: hidden;
}

.media-actions {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 10;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(-10px);
}

.media-item:hover .media-actions {
    opacity: 1;
    transform: translateY(0);
}

.media-actions .delete-media-btn {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.9) 0%, rgba(220, 38, 38, 0.9) 100%);
    backdrop-filter: blur(10px);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.4);
    border: 2px solid rgba(255, 255, 255, 0.3);
    font-size: 14px;
}

.media-actions .delete-media-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.6);
    background: linear-gradient(135deg, rgba(239, 68, 68, 1) 0%, rgba(220, 38, 38, 1) 100%);
}

.media-actions .delete-media-btn:active {
    transform: scale(0.95);
}

.media-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 2;
}

.media-item:hover::before {
    opacity: 1;
}

.media-item:hover {
    transform: translateY(-8px) rotateX(2deg);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.media-content img, .media-content video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.media-item:hover .media-content img, .media-item:hover .media-content video {
    transform: scale(1.05);
}

.media-content .video-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
    backdrop-filter: blur(10px);
    color: white;
    border-radius: 50%;
    width: 56px;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    z-index: 3;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.media-item:hover .media-content .video-overlay {
    transform: translate(-50%, -50%) scale(1.1);
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.9) 0%, rgba(22, 163, 74, 0.9) 100%);
}

/* 媒体预览弹窗 */
.media-preview-modal {
    background: rgba(0, 0, 0, 0.95);
}

.media-preview-container {
    width: 90%;
    height: 90%;
    max-width: 1200px;
    background: transparent;
    display: flex;
    flex-direction: column;
}

.media-preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
}

.media-preview-header .close-btn,
.media-preview-header .delete-media-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.media-preview-header .delete-media-btn {
    color: var(--error-color);
}

.media-preview-header .close-btn:hover,
.media-preview-header .delete-media-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.media-preview-content {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.media-container {
    max-width: 100%;
    max-height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.media-container img, .media-container video {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: var(--border-radius-lg);
}

.media-preview-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
}

.nav-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
}

.nav-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.media-counter {
    color: white;
    font-size: 16px;
    font-weight: 600;
}

/* 朋友圈删除确认弹窗 - 大厂风格设计 */
.moments-delete-confirm-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.75);
    backdrop-filter: blur(24px) saturate(180%);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    animation: fadeInBackdrop 0.4s ease-out forwards;
}

@keyframes fadeInBackdrop {
    from {
        opacity: 0;
        backdrop-filter: blur(0px) saturate(100%);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(24px) saturate(180%);
    }
}

.moments-delete-confirm-modal.active {
    display: flex;
    opacity: 1;
}

.moments-delete-confirm-container {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(32px) saturate(200%);
    border-radius: 28px;
    width: 90%;
    max-width: 420px;
    box-shadow:
        0 32px 80px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    transform: translateY(40px) scale(0.9);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.6);
}

.moments-delete-confirm-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg,
        rgba(255, 255, 255, 0.1) 0%,
        rgba(255, 255, 255, 0.05) 50%,
        rgba(0, 0, 0, 0.02) 100%);
    z-index: -1;
}

.moments-delete-confirm-modal.active .moments-delete-confirm-container {
    transform: translateY(0) scale(1);
    animation: slideInScale 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
}

.moments-delete-confirm-modal.closing {
    opacity: 0;
    transition: opacity 0.25s cubic-bezier(0.4, 0, 0.6, 1);
}

.moments-delete-confirm-modal.closing .moments-delete-confirm-container {
    transform: translateY(15px) scale(0.96);
    opacity: 0;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.6, 1);
}

@keyframes slideInScale {
    0% {
        transform: translateY(40px) scale(0.9);
        opacity: 0;
    }
    60% {
        transform: translateY(-8px) scale(1.02);
        opacity: 0.9;
    }
    100% {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

.moments-delete-confirm-content {
    text-align: center;
    padding: 48px 32px 32px;
    position: relative;
}

.moments-delete-confirm-icon {
    position: relative;
    margin-bottom: 32px;
    display: inline-block;
}

.moments-delete-confirm-icon i {
    font-size: 72px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 8px 16px rgba(239, 68, 68, 0.3));
    animation: pulseWarning 2s ease-in-out infinite;
    position: relative;
}

@keyframes pulseWarning {
    0%, 100% {
        transform: scale(1);
        filter: drop-shadow(0 8px 16px rgba(239, 68, 68, 0.3));
    }
    50% {
        transform: scale(1.05);
        filter: drop-shadow(0 12px 24px rgba(239, 68, 68, 0.4));
    }
}

.moments-delete-confirm-icon::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(239, 68, 68, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    z-index: -1;
    animation: rippleWarning 2s ease-in-out infinite;
}

@keyframes rippleWarning {
    0%, 100% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0.6;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.2;
    }
}

.moments-delete-confirm-title {
    font-size: 28px;
    font-weight: 800;
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 16px;
    letter-spacing: -0.8px;
    line-height: 1.2;
}

.moments-delete-confirm-message {
    font-size: 17px;
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 40px;
    font-weight: 500;
    max-width: 320px;
    margin-left: auto;
    margin-right: auto;
}

.moments-delete-confirm-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
    margin-top: 8px;
}

.moments-delete-cancel-btn, .moments-delete-confirm-btn {
    padding: 16px 32px;
    border: none;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 12px;
    position: relative;
    overflow: hidden;
    min-width: 120px;
    justify-content: center;
    letter-spacing: 0.5px;
    text-transform: none;
}

.moments-delete-cancel-btn {
    background: rgba(100, 116, 139, 0.08);
    color: #64748b;
    border: 2px solid rgba(100, 116, 139, 0.15);
    backdrop-filter: blur(10px);
}

.moments-delete-confirm-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
    color: white;
    box-shadow:
        0 8px 32px rgba(239, 68, 68, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.1) inset;
    border: 1px solid rgba(220, 38, 38, 0.3);
}

.moments-delete-cancel-btn::before, .moments-delete-confirm-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.moments-delete-cancel-btn:hover::before, .moments-delete-confirm-btn:hover::before {
    left: 100%;
}

.moments-delete-cancel-btn:hover {
    transform: translateY(-2px) scale(1.02);
    background: rgba(100, 116, 139, 0.12);
    border-color: rgba(100, 116, 139, 0.25);
    box-shadow: 0 8px 24px rgba(100, 116, 139, 0.15);
    color: #475569;
}

.moments-delete-confirm-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow:
        0 16px 48px rgba(239, 68, 68, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.2) inset;
    background: linear-gradient(135deg, #f87171 0%, #ef4444 50%, #dc2626 100%);
}

.moments-delete-cancel-btn:active {
    transform: translateY(0) scale(0.96);
    transition: all 0.1s cubic-bezier(0.4, 0, 0.6, 1);
}

.moments-delete-confirm-btn:active {
    transform: translateY(-1px) scale(0.96);
    transition: all 0.1s cubic-bezier(0.4, 0, 0.6, 1);
}

/* 按钮点击后的成功状态 */
.moments-delete-cancel-btn.clicked {
    transform: scale(0.98);
    opacity: 0.8;
    transition: all 0.15s ease-out;
}

.moments-delete-confirm-btn.clicked {
    transform: scale(0.98);
    opacity: 0.8;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 50%, #991b1b 100%);
    transition: all 0.15s ease-out;
}

/* 确认弹窗 - 保持原有样式用于相册等其他功能 */
.confirm-modal-content {
    text-align: center;
    padding: 40px 24px 24px;
}

.confirm-modal-content i {
    font-size: 48px;
    color: var(--warning-color);
    margin-bottom: 20px;
}

.confirm-modal-content h3 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
}

.confirm-modal-content p {
    font-size: 16px;
    color: var(--text-secondary);
    line-height: 1.5;
    margin-bottom: 24px;
}

.confirm-actions {
    display: flex;
    gap: 12px;
    justify-content: center;
}

/* 朋友圈删除确认弹窗响应式设计 */
@media (max-width: 768px) {
    .moments-delete-confirm-container {
        width: 95%;
        max-width: 360px;
        margin: 20px;
        border-radius: 24px;
    }

    .moments-delete-confirm-content {
        padding: 40px 24px 28px;
    }

    .moments-delete-confirm-icon i {
        font-size: 64px;
    }

    .moments-delete-confirm-title {
        font-size: 24px;
        margin-bottom: 12px;
    }

    .moments-delete-confirm-message {
        font-size: 16px;
        margin-bottom: 32px;
    }

    .moments-delete-confirm-actions {
        flex-direction: column;
        gap: 12px;
    }

    .moments-delete-cancel-btn, .moments-delete-confirm-btn {
        width: 100%;
        justify-content: center;
        padding: 16px 24px;
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .moments-delete-confirm-container {
        width: 96%;
        margin: 16px;
        border-radius: 20px;
    }

    .moments-delete-confirm-content {
        padding: 32px 20px 24px;
    }

    .moments-delete-confirm-icon i {
        font-size: 56px;
    }

    .moments-delete-confirm-title {
        font-size: 22px;
    }

    .moments-delete-confirm-message {
        font-size: 15px;
        margin-bottom: 28px;
    }
}

/* 页面加载动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.albums-grid {
    animation: fadeInUp 0.6s ease-out;
}

.album-card {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;
}

.album-card:nth-child(1) { animation-delay: 0.1s; }
.album-card:nth-child(2) { animation-delay: 0.2s; }
.album-card:nth-child(3) { animation-delay: 0.3s; }
.album-card:nth-child(4) { animation-delay: 0.4s; }
.album-card:nth-child(5) { animation-delay: 0.5s; }
.album-card:nth-child(6) { animation-delay: 0.6s; }

.albums-stats {
    animation: slideInLeft 0.8s ease-out;
}

.empty-state {
    animation: fadeInUp 0.8s ease-out;
}

/* 响应式设计 - 优化移动端体验 */
@media (max-width: 768px) {
    .page-header {
        padding: 16px 20px;
    }

    .page-header h2 {
        font-size: 24px;
    }

    .back-btn, .create-album-btn {
        width: 44px;
        height: 44px;
        border-radius: 14px;
    }

    .albums-content, .album-detail-content {
        padding: 20px;
    }

    .albums-stats {
        flex-direction: column;
        gap: 20px;
        padding: 24px;
    }

    .stat-item:not(:last-child)::after {
        display: none;
    }

    .stat-number {
        font-size: 28px;
    }

    .albums-grid {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 20px;
    }

    .media-grid {
        grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
        gap: 16px;
    }

    .album-modal-container {
        width: 95%;
        margin: 20px;
        border-radius: 24px;
    }

    .album-modal-header {
        padding: 24px;
    }

    .album-modal-content {
        padding: 24px;
    }

    .form-actions, .confirm-actions {
        flex-direction: column;
        gap: 12px;
    }

    .cancel-btn, .save-btn, .delete-btn {
        width: 100%;
        justify-content: center;
        padding: 16px;
    }

    .upload-area {
        padding: 32px 20px;
    }

    .upload-content i {
        font-size: 48px;
    }

    .upload-content h3 {
        font-size: 20px;
    }

    .media-preview-container {
        width: 95%;
        height: 95%;
    }

    .media-preview-header, .media-preview-footer {
        padding: 16px;
    }

    .album-info {
        padding: 24px;
    }

    .album-name {
        font-size: 24px;
    }

    .album-stats {
        flex-direction: column;
        gap: 12px;
    }

    .empty-state, .empty-media-state {
        padding: 50px 24px 40px;
        margin: 20px auto;
        border-radius: 24px;
    }

    .empty-state i, .empty-media-state i {
        font-size: 64px;
    }

    .empty-state h3, .empty-media-state h3 {
        font-size: 22px;
        margin-bottom: 12px;
    }

    .empty-state p, .empty-media-state p {
        font-size: 14px;
        margin-bottom: 32px;
    }

    .create-first-album-btn {
        padding: 14px 28px;
        font-size: 14px;
        border-radius: 20px;
    }
}
