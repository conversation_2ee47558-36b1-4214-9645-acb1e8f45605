import { test, expect } from '@playwright/test';

test('创建群禁用自选头像，群头像为成员九宫格', async ({ page }) => {
  // 创建测试用户
  const username = `groupAvatarA_${Date.now()}`;
  
  // 访问主页，会自动显示登录页面
  await page.goto('http://localhost:8080');
  await page.waitForSelector('#show-register', { timeout: 10000 });
  
  // 切换到注册表单
  await page.click('#show-register');
  await page.waitForSelector('#register-username', { timeout: 5000 });
  
  // 填写注册信息
  await page.fill('#register-username', username);
  await page.fill('#register-password', '123456');
  await page.click('#register-btn');
  
  // 等待注册成功
  await page.waitForTimeout(3000);
  
  // 检查登录结果
  const loginResult = await page.evaluate(() => {
    return JSON.parse(localStorage.getItem('wechat_user') || '{}');
  });
  console.log('登录结果:', loginResult);
  
  // 检查登录状态
  const loginStatus = await page.evaluate(() => {
    return fetch('/api/user/status', { method: 'GET' })
      .then(res => res.json())
      .catch(() => ({ code: 1 }));
  });
  console.log('登录状态检查:', loginStatus);
  
  // 获取页面标题
  const title = await page.title();
  console.log('页面标题:', title);
  
  // 检查JavaScript错误
  const jsErrors = await page.evaluate(() => {
    return window.jsErrors || [];
  });
  console.log('JavaScript错误:', jsErrors);
  
  // 检查showCreateGroupChat函数是否存在
  const showCreateGroupChatExists = await page.evaluate(() => {
    return typeof window.showCreateGroupChat === 'function';
  });
  console.log('showCreateGroupChat函数是否存在:', showCreateGroupChatExists);
  
  if (showCreateGroupChatExists) {
    // 创建群聊
    const createGroupResult = await page.evaluate(() => {
      return new Promise((resolve) => {
        // 模拟创建群聊
        window.showCreateGroupChat();
        
        // 等待创建群聊界面出现
        setTimeout(() => {
          const groupNameInput = document.getElementById('group-name');
          if (groupNameInput) {
            groupNameInput.value = '测试群九宫格';
            
            // 模拟选择成员（创建虚拟成员）
            window.selectedMembers = [
              { id: 1, name: '成员1', avatar: '' },
              { id: 2, name: '成员2', avatar: '' }
            ];
            
            // 调用创建群聊API
            fetch('/api/groups', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                name: '测试群九宫格',
                member_ids: [1, 2]
              })
            })
            .then(res => res.json())
            .then(data => {
              resolve(data);
            })
            .catch(err => {
              resolve({ code: 1, msg: err.message });
            });
          } else {
            resolve({ code: 1, msg: '找不到群名输入框' });
          }
        }, 1000);
      });
    });
    
    console.log('创建群聊响应:', createGroupResult);
    
    if (createGroupResult.code === 0) {
      // 等待群聊出现在聊天列表中
      await page.waitForTimeout(2000);
      
      // 检查聊天列表中是否有九宫格头像
      const hasGroupAvatar = await page.$$eval('.chat-item', (items) => {
        return items.some(item => {
          const avatarGrid = item.querySelector('.group-avatar-grid');
          return avatarGrid !== null;
        });
      });
      console.log('聊天列表中是否有九宫格头像:', hasGroupAvatar);
      
      if (hasGroupAvatar) {
        // 点击群聊进入聊天详情
        await page.click('.chat-item:has(.group-avatar-grid)');
        await page.waitForTimeout(1000);
        
        // 检查聊天详情是否已打开
        const chatDetailOpen = await page.$('#chat-detail') !== null;
        console.log('聊天详情是否已打开:', chatDetailOpen);
        
        if (chatDetailOpen) {
          // 检查更多按钮是否存在
          const moreButtonExists = await page.$('#more-btn') !== null;
          console.log('更多按钮是否存在:', moreButtonExists);
          
          if (moreButtonExists) {
            console.log('点击更多按钮');
            await page.click('#more-btn');
            await page.waitForTimeout(1000);
            
            // 检查群详情页面是否有大的九宫格头像
            const hasLargeGroupAvatar = await page.$('.group-avatar-large') !== null;
            console.log('群详情页面是否有group-avatar-large元素:', hasLargeGroupAvatar);
            
            if (hasLargeGroupAvatar) {
              const largeAvatarContent = await page.$eval('.group-avatar-large', el => el.innerHTML);
              console.log('group-avatar-large内容:', largeAvatarContent);
            }
            
            // 手动调用showChatDetail函数来确保群详情正确显示
            const manualCallResult = await page.evaluate((groupId) => {
              if (typeof window.showChatDetail === 'function') {
                window.showChatDetail(groupId, { type: 'group' });
                return { 
                  success: true, 
                  currentChatId: window.currentChatId,
                  isGroup: window.currentChatObject && window.currentChatObject.type === 'group'
                };
              }
              return { success: false };
            }, createGroupResult.data.id);
            
            console.log('手动调用结果:', manualCallResult);
            
            // 获取群详情API数据
            const groupDetailResult = await page.evaluate((groupId) => {
              return fetch(`/api/groups/${groupId}`)
                .then(res => res.json())
                .catch(err => ({ code: 1, msg: err.message }));
            }, createGroupResult.data.id);
            
            console.log('群详情API结果:', groupDetailResult);
            
            // 最终检查群详情中是否有大九宫格头像
            const finalCheck = await page.$('.group-avatar-large') !== null;
            console.log('群详情中是否有大九宫格头像:', finalCheck);
            
            expect(finalCheck).toBe(true);
          }
        }
      }
    }
  }
});
